# Video AI Generator

A complete AI system for training video generation models and creating new videos from your training data.

## Features

- **Video Upload & Processing**: Upload training videos and automatically preprocess them
- **Custom Model Training**: Train your own video generation model using ConvLSTM and GAN architecture
- **Multiple Generation Modes**: Generate videos from random noise or seed videos
- **Web Interface**: Easy-to-use web interface for all operations
- **Real-time Training Monitoring**: Track training progress in real-time

## Requirements

### Hardware Requirements
- **GPU**: NVIDIA GPU with CUDA support (RTX 3080+ recommended)
- **RAM**: At least 16GB RAM (32GB+ recommended)
- **Storage**: At least 50GB free space for models and data

### Software Requirements
- Python 3.8+
- CUDA 11.0+ (for GPU acceleration)
- FFmpeg (for video processing)

## Installation

1. **Clone or download this project to your workspace**

2. **Install dependencies**:
   ```bash
   python setup.py
   ```

3. **Verify installation**:
   ```bash
   python -c "import torch; print('CUDA available:', torch.cuda.is_available())"
   ```

## Quick Start

### 1. Setup Environment
```bash
python setup.py
```

### 2. Prepare Training Data
- Add your training videos to `data/raw_videos/`
- Supported formats: MP4, AVI, MOV, MKV
- Recommended: 10-50 videos, each 5-30 seconds long

### 3. Process Videos
```bash
python video_processor.py
```

### 4. Train Model
```bash
python train_model.py
```

### 5. Generate Videos
```bash
python generate_video.py --model models/checkpoints/checkpoint_epoch_50.pth --mode random --frames 16
```

## Web Interface

Start the web application:
```bash
python web_app.py
```

Then open your browser to `http://localhost:5000`

### Web Interface Features:
- **Upload Page**: Upload multiple training videos
- **Training Dashboard**: Monitor training progress in real-time
- **Generation Interface**: Generate videos with different modes
- **Model Management**: View and manage trained models

## Usage Examples

### Command Line Generation

**Random Video Generation**:
```bash
python generate_video.py --model models/checkpoints/latest.pth --mode random --frames 24
```

**Seed-based Generation**:
```bash
python generate_video.py --model models/checkpoints/latest.pth --mode seed --seed_video path/to/seed.mp4 --frames 16
```

**Video Interpolation**:
```bash
python generate_video.py --model models/checkpoints/latest.pth --mode interpolate --video1 video1.mp4 --video2 video2.mp4
```

### Python API

```python
from generate_video import VideoGeneratorInterface

# Load trained model
generator = VideoGeneratorInterface('models/checkpoints/latest.pth')

# Generate random video
output_path = generator.generate_random_video(num_frames=16)

# Generate from seed video
output_path = generator.generate_from_seed('seed_video.mp4', num_future_frames=20)
```

## Model Architecture

The system uses a hybrid architecture combining:

- **ConvLSTM**: For temporal sequence modeling
- **3D CNNs**: For spatiotemporal feature extraction
- **GAN Framework**: Adversarial training for realistic video generation
- **Encoder-Decoder**: For video compression and generation

### Key Components:

1. **VideoGenerator**: Main generation model using ConvLSTM cells
2. **VideoDiscriminator**: 3D CNN discriminator for adversarial training
3. **VideoProcessor**: Preprocessing pipeline for training data
4. **VideoGANTrainer**: Training loop with loss functions

## Training Tips

### Data Preparation:
- Use consistent video quality and resolution
- Similar content/style works best for coherent results
- At least 20-30 videos recommended for good results
- Videos should be 5-30 seconds long

### Training Parameters:
- **Batch Size**: Start with 2-4 (depends on GPU memory)
- **Learning Rate**: 0.0002 (default) works well
- **Epochs**: 50-200 depending on data size
- **Sequence Length**: 16 frames (adjustable)

### Hardware Optimization:
- Use mixed precision training for faster training
- Monitor GPU memory usage
- Use data loading with multiple workers

## File Structure

```
video-ai-generator/
├── data/
│   ├── raw_videos/          # Original training videos
│   ├── processed_videos/    # Preprocessed sequences
│   └── frames/             # Extracted frames
├── models/
│   ├── checkpoints/        # Training checkpoints
│   └── trained/           # Final trained models
├── outputs/
│   └── generated_videos/   # Generated video outputs
├── templates/             # Web interface templates
├── static/               # Web assets
├── logs/                # Training logs
├── model.py             # Model architectures
├── train_model.py       # Training script
├── video_processor.py   # Video preprocessing
├── generate_video.py    # Video generation
├── web_app.py          # Web interface
└── setup.py            # Setup script
```

## Troubleshooting

### Common Issues:

**CUDA Out of Memory**:
- Reduce batch size in training
- Use smaller sequence length
- Enable gradient checkpointing

**Poor Generation Quality**:
- Train for more epochs
- Use more training data
- Adjust learning rates
- Check data quality

**Slow Training**:
- Ensure CUDA is properly installed
- Use multiple data loading workers
- Consider mixed precision training

**Web Interface Issues**:
- Check if all dependencies are installed
- Ensure proper file permissions
- Check browser console for errors

## Advanced Usage

### Custom Model Architecture:
Modify `model.py` to experiment with different architectures:
- Change ConvLSTM hidden dimensions
- Adjust number of layers
- Modify loss functions

### Custom Training Loop:
Extend `train_model.py` for:
- Custom loss functions
- Learning rate scheduling
- Advanced data augmentation

### Batch Processing:
Process multiple videos efficiently:
```python
from video_processor import VideoProcessor
processor = VideoProcessor()
processor.process_all_videos(sequence_length=16)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. Feel free to use and modify for your needs.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the code documentation
3. Create an issue with detailed information

## Acknowledgments

- Built with PyTorch and OpenCV
- Inspired by state-of-the-art video generation research
- Uses ConvLSTM and GAN architectures
