"""
Professional Configuration System for Video AI Generator
Comprehensive settings for optimal performance on different hardware configurations
"""

import torch
import os
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple
import json

@dataclass
class ModelConfig:
    """Model architecture configuration"""
    input_channels: int = 3
    hidden_dims: List[int] = field(default_factory=lambda: [64, 128, 256, 512])
    disc_hidden_dims: List[int] = field(default_factory=lambda: [64, 128, 256, 512])
    kernel_size: Tuple[int, int] = (3, 3)
    num_layers: int = 4
    sequence_length: int = 16
    use_attention: bool = True
    use_progressive: bool = True
    max_resolution: int = 256
    dropout_rate: float = 0.1
    
@dataclass
class TrainingConfig:
    """Training configuration"""
    batch_size: int = 4
    num_epochs: int = 100
    lr_g: float = 0.0002
    lr_d: float = 0.0002
    beta1: float = 0.5
    beta2: float = 0.999
    weight_decay: float = 1e-4
    
    # Progressive training
    progressive_epochs: List[int] = field(default_factory=lambda: [20, 40, 60])
    progressive_batch_sizes: List[int] = field(default_factory=lambda: [8, 6, 4])
    
    # Loss weights
    adversarial_weight: float = 1.0
    reconstruction_weight: float = 10.0
    perceptual_weight: float = 5.0
    temporal_weight: float = 2.0
    gradient_weight: float = 1.0
    
    # Optimization
    use_mixed_precision: bool = True
    gradient_clip_val: float = 1.0
    accumulate_grad_batches: int = 1
    
    # Scheduling
    scheduler_type: str = "cosine_warm_restarts"  # cosine_warm_restarts, reduce_on_plateau, exponential
    warmup_epochs: int = 5
    
    # Early stopping
    early_stopping_patience: int = 15
    early_stopping_min_delta: float = 1e-4
    
@dataclass
class DataConfig:
    """Data processing configuration"""
    data_dir: str = "data/processed_videos"
    raw_data_dir: str = "data/raw_videos"
    sequence_length: int = 16
    target_fps: int = 30
    target_resolution: Tuple[int, int] = (256, 256)
    
    # Augmentation
    use_augmentation: bool = True
    horizontal_flip_prob: float = 0.5
    rotation_range: int = 10
    brightness_range: float = 0.2
    contrast_range: float = 0.2
    
    # Data loading
    num_workers: int = 4
    pin_memory: bool = True
    prefetch_factor: int = 2
    
@dataclass
class SystemConfig:
    """System and hardware configuration"""
    device: str = "auto"  # auto, cpu, cuda, cuda:0, etc.
    multi_gpu: bool = False
    mixed_precision: bool = True
    compile_model: bool = False  # PyTorch 2.0 compilation
    
    # Memory optimization
    gradient_checkpointing: bool = True
    cpu_offload: bool = False
    
    # Monitoring
    use_wandb: bool = False
    wandb_project: str = "professional-video-ai"
    log_interval: int = 10
    save_interval: int = 5
    
    # Paths
    checkpoint_dir: str = "models/checkpoints"
    output_dir: str = "outputs/generated_videos"
    log_dir: str = "logs"

class ConfigManager:
    """Professional configuration management system"""
    
    def __init__(self):
        self.model_config = ModelConfig()
        self.training_config = TrainingConfig()
        self.data_config = DataConfig()
        self.system_config = SystemConfig()
        
        # Auto-detect optimal settings
        self._auto_configure()
    
    def _auto_configure(self):
        """Automatically configure based on available hardware"""
        # Detect GPU capabilities
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9  # GB
            gpu_name = torch.cuda.get_device_name(0)
            
            print(f"Detected GPU: {gpu_name} with {gpu_memory:.1f}GB memory")
            
            # Optimize settings based on GPU memory
            if gpu_memory >= 24:  # High-end GPU (RTX 4090, A100, etc.)
                self.training_config.batch_size = 8
                self.model_config.hidden_dims = [64, 128, 256, 512, 1024]
                self.model_config.max_resolution = 512
                self.data_config.target_resolution = (512, 512)
                print("Configured for high-end GPU - Maximum quality settings")
                
            elif gpu_memory >= 12:  # Mid-high GPU (RTX 3080, 4070 Ti, etc.)
                self.training_config.batch_size = 6
                self.model_config.hidden_dims = [64, 128, 256, 512]
                self.model_config.max_resolution = 256
                self.data_config.target_resolution = (256, 256)
                print("Configured for mid-high GPU - High quality settings")
                
            elif gpu_memory >= 8:  # Mid-range GPU (RTX 3070, 4060 Ti, etc.)
                self.training_config.batch_size = 4
                self.model_config.hidden_dims = [32, 64, 128, 256]
                self.model_config.max_resolution = 256
                self.data_config.target_resolution = (256, 256)
                print("Configured for mid-range GPU - Balanced settings")
                
            elif gpu_memory >= 6:  # Entry GPU (RTX 3060, 4060, etc.)
                self.training_config.batch_size = 2
                self.model_config.hidden_dims = [32, 64, 128]
                self.model_config.max_resolution = 128
                self.data_config.target_resolution = (128, 128)
                self.system_config.gradient_checkpointing = True
                print("Configured for entry-level GPU - Memory optimized settings")
                
            else:  # Low memory GPU
                self.training_config.batch_size = 1
                self.model_config.hidden_dims = [32, 64]
                self.model_config.max_resolution = 64
                self.data_config.target_resolution = (64, 64)
                self.system_config.gradient_checkpointing = True
                self.system_config.cpu_offload = True
                print("Configured for low-memory GPU - Minimal settings")
                
        else:  # CPU only
            self.training_config.batch_size = 1
            self.model_config.hidden_dims = [32, 64]
            self.model_config.max_resolution = 64
            self.data_config.target_resolution = (64, 64)
            self.training_config.use_mixed_precision = False
            self.system_config.mixed_precision = False
            self.data_config.num_workers = min(4, os.cpu_count())
            print("Configured for CPU training - This will be very slow!")
        
        # Detect CPU capabilities
        cpu_count = os.cpu_count()
        self.data_config.num_workers = min(self.data_config.num_workers, cpu_count)
        
        # Auto-adjust based on available RAM
        try:
            import psutil
            ram_gb = psutil.virtual_memory().total / 1e9
            if ram_gb < 8:
                self.data_config.num_workers = 2
                self.training_config.accumulate_grad_batches = 2
                print(f"Low RAM detected ({ram_gb:.1f}GB) - Reduced workers and gradient accumulation")
        except ImportError:
            pass
    
    def get_optimal_config_for_task(self, task: str) -> Dict:
        """Get optimal configuration for specific tasks"""
        configs = {
            "quick_test": {
                "num_epochs": 5,
                "batch_size": 2,
                "sequence_length": 8,
                "target_resolution": (64, 64),
                "save_interval": 1
            },
            "high_quality": {
                "num_epochs": 200,
                "use_progressive": True,
                "perceptual_weight": 10.0,
                "target_resolution": (512, 512) if torch.cuda.is_available() else (256, 256)
            },
            "fast_training": {
                "use_attention": False,
                "use_progressive": False,
                "mixed_precision": True,
                "compile_model": True
            },
            "memory_efficient": {
                "gradient_checkpointing": True,
                "cpu_offload": True,
                "batch_size": 1,
                "accumulate_grad_batches": 4
            }
        }
        
        return configs.get(task, {})
    
    def save_config(self, filepath: str):
        """Save configuration to file"""
        config_dict = {
            "model": self.model_config.__dict__,
            "training": self.training_config.__dict__,
            "data": self.data_config.__dict__,
            "system": self.system_config.__dict__
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        print(f"Configuration saved to {filepath}")
    
    def load_config(self, filepath: str):
        """Load configuration from file"""
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        
        # Update configurations
        for key, value in config_dict.get("model", {}).items():
            setattr(self.model_config, key, value)
        
        for key, value in config_dict.get("training", {}).items():
            setattr(self.training_config, key, value)
        
        for key, value in config_dict.get("data", {}).items():
            setattr(self.data_config, key, value)
        
        for key, value in config_dict.get("system", {}).items():
            setattr(self.system_config, key, value)
        
        print(f"Configuration loaded from {filepath}")
    
    def print_config(self):
        """Print current configuration"""
        print("\n" + "="*60)
        print("PROFESSIONAL VIDEO AI GENERATOR - CONFIGURATION")
        print("="*60)
        
        print(f"\nMODEL CONFIGURATION:")
        print(f"  Architecture: Progressive ConvLSTM + GAN")
        print(f"  Hidden Dimensions: {self.model_config.hidden_dims}")
        print(f"  Max Resolution: {self.model_config.max_resolution}x{self.model_config.max_resolution}")
        print(f"  Attention: {'Enabled' if self.model_config.use_attention else 'Disabled'}")
        print(f"  Progressive Training: {'Enabled' if self.model_config.use_progressive else 'Disabled'}")
        
        print(f"\nTRAINING CONFIGURATION:")
        print(f"  Batch Size: {self.training_config.batch_size}")
        print(f"  Learning Rates: G={self.training_config.lr_g}, D={self.training_config.lr_d}")
        print(f"  Mixed Precision: {'Enabled' if self.training_config.use_mixed_precision else 'Disabled'}")
        print(f"  Progressive Epochs: {self.training_config.progressive_epochs}")
        
        print(f"\nDATA CONFIGURATION:")
        print(f"  Target Resolution: {self.data_config.target_resolution}")
        print(f"  Sequence Length: {self.data_config.sequence_length}")
        print(f"  Data Workers: {self.data_config.num_workers}")
        print(f"  Augmentation: {'Enabled' if self.data_config.use_augmentation else 'Disabled'}")
        
        print(f"\nSYSTEM CONFIGURATION:")
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"  Device: {device}")
        if torch.cuda.is_available():
            print(f"  GPU: {torch.cuda.get_device_name(0)}")
            print(f"  GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
        print(f"  Gradient Checkpointing: {'Enabled' if self.system_config.gradient_checkpointing else 'Disabled'}")
        
        print("="*60)

def create_config_for_pc_specs():
    """Create optimized configuration based on PC specifications"""
    config_manager = ConfigManager()
    
    # Save default optimized config
    config_manager.save_config("configs/optimized_config.json")
    
    # Create task-specific configs
    tasks = ["quick_test", "high_quality", "fast_training", "memory_efficient"]
    
    for task in tasks:
        task_config = config_manager.get_optimal_config_for_task(task)
        
        # Apply task-specific settings
        for category in ["model", "training", "data", "system"]:
            config_obj = getattr(config_manager, f"{category}_config")
            for key, value in task_config.items():
                if hasattr(config_obj, key):
                    setattr(config_obj, key, value)
        
        config_manager.save_config(f"configs/{task}_config.json")
    
    config_manager.print_config()
    return config_manager

if __name__ == "__main__":
    # Create optimized configurations
    config_manager = create_config_for_pc_specs()
    print("\nOptimized configurations created for your PC!")
    print("Available configs: optimized_config.json, quick_test_config.json, high_quality_config.json")
