"""
Demo script to test the Video AI Generator
"""

import os
import sys

def check_installation():
    """Check if all required packages are installed"""
    print("Checking Video AI Generator installation...")
    print("=" * 50)
    
    required_packages = [
        'torch', 'torchvision', 'opencv-python', 'numpy', 
        'PIL', 'matplotlib', 'flask', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"✓ {package} - OK")
            elif package == 'opencv-python':
                import cv2
                print(f"✓ {package} - OK")
            else:
                __import__(package)
                print(f"✓ {package} - OK")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    print("=" * 50)
    
    if missing_packages:
        print(f"Missing packages: {', '.join(missing_packages)}")
        print("Please run: python setup.py")
        return False
    else:
        print("All packages installed successfully!")
        return True

def check_gpu():
    """Check GPU availability"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA available! GPU: {torch.cuda.get_device_name(0)}")
            print(f"✓ CUDA Version: {torch.version.cuda}")
            return True
        else:
            print("⚠ CUDA not available. Training will be slow on CPU.")
            return False
    except ImportError:
        print("PyTorch not installed.")
        return False

def check_directories():
    """Check if required directories exist"""
    print("\nChecking directory structure...")
    
    required_dirs = [
        "data/raw_videos",
        "data/processed_videos", 
        "data/frames",
        "models/checkpoints",
        "models/trained",
        "outputs/generated_videos",
        "logs",
        "static/uploads"
    ]
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ {directory}")
        else:
            print(f"✗ {directory} - Creating...")
            os.makedirs(directory, exist_ok=True)

def test_model_architecture():
    """Test if model architecture works"""
    print("\nTesting model architecture...")
    
    try:
        from model import VideoGenerator, VideoDiscriminator
        import torch
        
        # Test with small dimensions for quick testing
        generator = VideoGenerator(input_channels=3)
        discriminator = VideoDiscriminator(input_channels=3)
        
        # Create test input
        batch_size = 1
        seq_len = 8
        channels = 3
        height, width = 64, 64
        
        test_input = torch.randn(batch_size, seq_len, channels, height, width)
        
        # Test generator
        with torch.no_grad():
            generated = generator(test_input, future_frames=4)
            print(f"✓ Generator test passed - Output shape: {generated.shape}")
        
        # Test discriminator
        with torch.no_grad():
            disc_output = discriminator(generated)
            print(f"✓ Discriminator test passed - Output shape: {disc_output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model test failed: {str(e)}")
        return False

def show_usage():
    """Show usage instructions"""
    print("\n" + "=" * 60)
    print("VIDEO AI GENERATOR - USAGE INSTRUCTIONS")
    print("=" * 60)
    
    print("\n1. SETUP (First time only):")
    print("   python setup.py")
    
    print("\n2. PREPARE TRAINING DATA:")
    print("   - Add video files to data/raw_videos/")
    print("   - Supported formats: MP4, AVI, MOV, MKV")
    print("   - Recommended: 10-50 videos, 5-30 seconds each")
    
    print("\n3. PROCESS VIDEOS:")
    print("   python video_processor.py")
    
    print("\n4. TRAIN MODEL:")
    print("   python train_model.py")
    
    print("\n5. GENERATE VIDEOS:")
    print("   python generate_video.py --model models/checkpoints/latest.pth --mode random")
    
    print("\n6. WEB INTERFACE:")
    print("   python web_app.py")
    print("   Then open: http://localhost:5000")
    
    print("\n" + "=" * 60)
    print("IMPORTANT NOTES:")
    print("- GPU with CUDA is highly recommended for training")
    print("- Training can take hours to days depending on data size")
    print("- Start with small datasets for testing")
    print("- Check README.md for detailed instructions")
    print("=" * 60)

def main():
    """Main demo function"""
    print("Video AI Generator Demo")
    print("=" * 30)
    
    # Check installation
    if not check_installation():
        print("\nPlease install missing packages first.")
        return
    
    # Check GPU
    check_gpu()
    
    # Check directories
    check_directories()
    
    # Test model architecture
    test_model_architecture()
    
    # Show usage instructions
    show_usage()

if __name__ == "__main__":
    main()
