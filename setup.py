"""
Setup script for Video AI Generator
"""

import os
import subprocess
import sys

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def check_gpu():
    """Check if CUDA is available"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA is available! GPU: {torch.cuda.get_device_name(0)}")
            print(f"✓ CUDA Version: {torch.version.cuda}")
            return True
        else:
            print("⚠ CUDA not available. Training will be slow on CPU.")
            return False
    except ImportError:
        print("PyTorch not installed yet.")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "data/raw_videos",
        "data/processed_videos", 
        "data/frames",
        "models/checkpoints",
        "models/trained",
        "outputs/generated_videos",
        "logs",
        "static/uploads"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def main():
    print("Setting up Video AI Generator...")
    print("=" * 50)
    
    # Install requirements
    install_requirements()
    
    # Check GPU availability
    check_gpu()
    
    # Create directories
    create_directories()
    
    print("=" * 50)
    print("Setup complete! You can now start building your video AI model.")
    print("\nNext steps:")
    print("1. Add training videos to data/raw_videos/")
    print("2. Run python video_processor.py to preprocess videos")
    print("3. Run python train_model.py to start training")

if __name__ == "__main__":
    main()
