"""
Professional Web Interface for Video AI Generator
Advanced UI with real-time monitoring, batch processing, and comprehensive controls
"""

from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, session
from werkzeug.utils import secure_filename
import os
import json
import threading
import time
from datetime import datetime
import psutil
import torch
import GP<PERSON>til
from config import ConfigManager
from professional_trainer import ProfessionalTrainer
import logging

app = Flask(__name__)
app.config['SECRET_KEY'] = 'professional-video-ai-secret-key'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 1GB max file size

# Global state management
class AppState:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.trainer = None
        self.training_thread = None
        self.system_monitor = SystemMonitor()
        self.training_status = {
            "is_training": False,
            "current_epoch": 0,
            "total_epochs": 0,
            "current_resolution": 64,
            "losses": {"generator": [], "discriminator": [], "perceptual": []},
            "metrics": {"fid_score": None, "lpips_score": None},
            "eta": None,
            "gpu_usage": 0,
            "memory_usage": 0
        }

class SystemMonitor:
    """Real-time system monitoring"""
    
    def __init__(self):
        self.monitoring = False
        self.stats = {
            "cpu_percent": 0,
            "memory_percent": 0,
            "gpu_percent": 0,
            "gpu_memory_percent": 0,
            "disk_usage": 0,
            "temperature": 0
        }
    
    def start_monitoring(self):
        """Start system monitoring thread"""
        self.monitoring = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
    
    def _monitor_loop(self):
        """Continuous monitoring loop"""
        while self.monitoring:
            try:
                # CPU and RAM
                self.stats["cpu_percent"] = psutil.cpu_percent(interval=1)
                self.stats["memory_percent"] = psutil.virtual_memory().percent
                
                # GPU monitoring
                if torch.cuda.is_available():
                    try:
                        gpus = GPUtil.getGPUs()
                        if gpus:
                            gpu = gpus[0]
                            self.stats["gpu_percent"] = gpu.load * 100
                            self.stats["gpu_memory_percent"] = gpu.memoryUtil * 100
                            self.stats["temperature"] = gpu.temperature
                    except:
                        pass
                
                # Disk usage
                disk = psutil.disk_usage('.')
                self.stats["disk_usage"] = (disk.used / disk.total) * 100
                
            except Exception as e:
                logging.error(f"Monitoring error: {e}")
            
            time.sleep(2)
    
    def get_stats(self):
        """Get current system statistics"""
        return self.stats.copy()

# Global app state
app_state = AppState()

@app.route('/')
def professional_dashboard():
    """Professional dashboard with comprehensive overview"""
    return render_template('professional_dashboard.html', 
                         config=app_state.config_manager,
                         system_stats=app_state.system_monitor.get_stats())

@app.route('/api/system_stats')
def get_system_stats():
    """API endpoint for real-time system statistics"""
    return jsonify(app_state.system_monitor.get_stats())

@app.route('/api/training_status')
def get_training_status():
    """Get detailed training status"""
    return jsonify(app_state.training_status)

@app.route('/configuration')
def configuration_page():
    """Advanced configuration interface"""
    return render_template('configuration.html', 
                         config=app_state.config_manager)

@app.route('/api/save_config', methods=['POST'])
def save_configuration():
    """Save custom configuration"""
    try:
        config_data = request.json
        
        # Update configuration
        for category, settings in config_data.items():
            if hasattr(app_state.config_manager, f"{category}_config"):
                config_obj = getattr(app_state.config_manager, f"{category}_config")
                for key, value in settings.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
        
        # Save to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        config_path = f"configs/custom_config_{timestamp}.json"
        app_state.config_manager.save_config(config_path)
        
        return jsonify({
            "success": True,
            "message": f"Configuration saved to {config_path}",
            "config_path": config_path
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/load_config', methods=['POST'])
def load_configuration():
    """Load configuration from file"""
    try:
        config_path = request.json.get('config_path')
        app_state.config_manager.load_config(config_path)
        
        return jsonify({
            "success": True,
            "message": f"Configuration loaded from {config_path}"
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/auto_configure', methods=['POST'])
def auto_configure():
    """Auto-configure based on hardware and task"""
    try:
        task = request.json.get('task', 'balanced')
        
        # Reset to auto-detected configuration
        app_state.config_manager = ConfigManager()
        
        # Apply task-specific optimizations
        if task != 'balanced':
            task_config = app_state.config_manager.get_optimal_config_for_task(task)
            # Apply task-specific settings (implementation details)
        
        return jsonify({
            "success": True,
            "message": f"Auto-configured for {task} task",
            "config": {
                "model": app_state.config_manager.model_config.__dict__,
                "training": app_state.config_manager.training_config.__dict__,
                "data": app_state.config_manager.data_config.__dict__,
                "system": app_state.config_manager.system_config.__dict__
            }
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/training')
def training_interface():
    """Advanced training interface"""
    return render_template('training_interface.html')

@app.route('/api/start_professional_training', methods=['POST'])
def start_professional_training():
    """Start professional training with advanced features"""
    if app_state.training_status["is_training"]:
        return jsonify({
            "success": False,
            "error": "Training already in progress"
        })
    
    try:
        # Get training parameters
        training_params = request.json
        
        # Update configuration with training parameters
        for key, value in training_params.items():
            if hasattr(app_state.config_manager.training_config, key):
                setattr(app_state.config_manager.training_config, key, value)
        
        # Initialize professional trainer
        config_dict = {
            **app_state.config_manager.model_config.__dict__,
            **app_state.config_manager.training_config.__dict__,
            **app_state.config_manager.data_config.__dict__,
            **app_state.config_manager.system_config.__dict__
        }
        
        app_state.trainer = ProfessionalTrainer(config_dict)
        
        # Start training in background thread
        app_state.training_thread = threading.Thread(
            target=run_professional_training,
            daemon=True
        )
        app_state.training_thread.start()
        
        return jsonify({
            "success": True,
            "message": "Professional training started",
            "config": config_dict
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

def run_professional_training():
    """Run professional training with real-time updates"""
    try:
        app_state.training_status["is_training"] = True
        app_state.training_status["total_epochs"] = app_state.config_manager.training_config.num_epochs
        
        # Load dataset
        from train_model import VideoDataset
        from torch.utils.data import DataLoader
        
        dataset = VideoDataset(sequence_length=app_state.config_manager.data_config.sequence_length)
        dataloader = DataLoader(
            dataset,
            batch_size=app_state.config_manager.training_config.batch_size,
            shuffle=True,
            num_workers=app_state.config_manager.data_config.num_workers,
            pin_memory=app_state.config_manager.data_config.pin_memory
        )
        
        # Training loop with status updates
        for epoch in range(app_state.config_manager.training_config.num_epochs):
            app_state.training_status["current_epoch"] = epoch + 1
            
            # Update current resolution for progressive training
            if app_state.config_manager.model_config.use_progressive:
                progressive_epochs = app_state.config_manager.training_config.progressive_epochs
                if epoch < progressive_epochs[0]:
                    app_state.training_status["current_resolution"] = 64
                elif epoch < progressive_epochs[1]:
                    app_state.training_status["current_resolution"] = 128
                else:
                    app_state.training_status["current_resolution"] = 256
            
            # Simulate training step (replace with actual training)
            time.sleep(1)  # Placeholder for actual training time
            
            # Update losses (placeholder values)
            app_state.training_status["losses"]["generator"].append(0.5 + 0.1 * (epoch % 10))
            app_state.training_status["losses"]["discriminator"].append(0.3 + 0.05 * (epoch % 8))
            app_state.training_status["losses"]["perceptual"].append(0.2 + 0.02 * (epoch % 12))
            
            # Keep only last 100 values for memory efficiency
            for loss_type in app_state.training_status["losses"]:
                if len(app_state.training_status["losses"][loss_type]) > 100:
                    app_state.training_status["losses"][loss_type] = \
                        app_state.training_status["losses"][loss_type][-100:]
        
        app_state.training_status["is_training"] = False
        logging.info("Professional training completed successfully")
        
    except Exception as e:
        app_state.training_status["is_training"] = False
        logging.error(f"Training failed: {e}")

@app.route('/generation')
def generation_interface():
    """Advanced video generation interface"""
    return render_template('generation_interface.html')

@app.route('/api/batch_generate', methods=['POST'])
def batch_generate_videos():
    """Batch video generation with advanced controls"""
    try:
        generation_params = request.json
        
        # Process batch generation request
        results = []
        for i in range(generation_params.get('batch_size', 1)):
            # Placeholder for actual generation
            result = {
                "video_id": f"generated_{i+1}_{int(time.time())}",
                "status": "completed",
                "path": f"outputs/generated_videos/batch_video_{i+1}.mp4",
                "metadata": {
                    "resolution": generation_params.get('resolution', '256x256'),
                    "frames": generation_params.get('frames', 16),
                    "style": generation_params.get('style', 'default')
                }
            }
            results.append(result)
        
        return jsonify({
            "success": True,
            "message": f"Generated {len(results)} videos",
            "results": results
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/upload')
def upload_page():
    """Video upload page"""
    return render_template('upload.html')

@app.route('/upload', methods=['POST'])
def upload_videos():
    """Handle video uploads"""
    try:
        if 'files[]' not in request.files:
            return jsonify({'success': False, 'error': 'No files selected'})

        files = request.files.getlist('files[]')
        uploaded_files = []

        os.makedirs('data/raw_videos', exist_ok=True)

        for file in files:
            if file and file.filename:
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename

                filepath = os.path.join('data/raw_videos', filename)
                file.save(filepath)
                uploaded_files.append(filename)

        return jsonify({
            'success': True,
            'uploaded_files': uploaded_files,
            'message': f'Uploaded {len(uploaded_files)} videos successfully'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/process_videos', methods=['POST'])
def process_videos():
    """Process uploaded videos"""
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor()
        sequences, metadata = processor.process_all_videos()

        return jsonify({
            'success': True,
            'sequences_count': len(sequences) if sequences else 0,
            'message': 'Videos processed successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dataset_info')
def get_dataset_info():
    """Get dataset information"""
    try:
        raw_videos_dir = 'data/raw_videos'
        total_videos = 0
        total_size = 0

        if os.path.exists(raw_videos_dir):
            for filename in os.listdir(raw_videos_dir):
                if filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    total_videos += 1
                    filepath = os.path.join(raw_videos_dir, filename)
                    total_size += os.path.getsize(filepath)

        processed_dir = 'data/processed_videos'
        processed_videos = 0
        if os.path.exists(processed_dir):
            processed_videos = len([f for f in os.listdir(processed_dir) if f.endswith('.pt')])

        return jsonify({
            'total_videos': total_videos,
            'total_size_mb': round(total_size / (1024*1024), 1),
            'processed_videos': processed_videos
        })

    except Exception as e:
        return jsonify({
            'total_videos': 0,
            'total_size_mb': 0,
            'processed_videos': 0
        })

@app.route('/analytics')
def analytics_dashboard():
    """Advanced analytics and monitoring dashboard"""
    return render_template('analytics_dashboard.html')

@app.route('/api/training_analytics')
def get_training_analytics():
    """Get comprehensive training analytics"""
    return jsonify({
        "training_history": app_state.training_status["losses"],
        "system_performance": app_state.system_monitor.get_stats(),
        "model_metrics": app_state.training_status["metrics"],
        "hardware_utilization": {
            "gpu_efficiency": 85.2,  # Placeholder
            "memory_efficiency": 78.5,  # Placeholder
            "training_speed": "2.3 it/s"  # Placeholder
        }
    })

def create_professional_templates():
    """Create professional HTML templates"""
    os.makedirs('templates', exist_ok=True)
    
    # Professional base template with advanced styling
    base_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Video AI Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .main-content { background-color: #f8f9fa; min-height: 100vh; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); border: none; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; }
        .status-running { background-color: #28a745; animation: pulse 2s infinite; }
        .status-idle { background-color: #6c757d; }
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 sidebar p-3">
                <h4 class="text-white mb-4"><i class="fas fa-video"></i> Video AI Pro</h4>
                <ul class="nav flex-column">
                    <li class="nav-item"><a class="nav-link text-white" href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/configuration"><i class="fas fa-cog"></i> Configuration</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/training"><i class="fas fa-brain"></i> Training</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/generation"><i class="fas fa-magic"></i> Generation</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </nav>
            <main class="col-md-10 main-content p-4">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>'''
    
    with open('templates/professional_base.html', 'w') as f:
        f.write(base_template)

if __name__ == '__main__':
    # Initialize system monitoring
    app_state.system_monitor.start_monitoring()
    
    # Create professional templates
    create_professional_templates()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Professional Video AI Generator Starting...")
    print("🔧 Auto-configuring for your hardware...")
    app_state.config_manager.print_config()
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
