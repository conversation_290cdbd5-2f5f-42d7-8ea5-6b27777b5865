"""
Test video generation functionality
"""

import torch
import cv2
import numpy as np
import os
from model import ProgressiveVideoGenerator
from PIL import Image

def create_sample_video():
    """Create a sample generated video"""
    print("🎨 Creating Sample Generated Video...")
    
    try:
        device = torch.device('cpu')
        
        # Create generator with matching dimensions
        generator = ProgressiveVideoGenerator(
            input_channels=3,
            hidden_dims=[32, 64],
            num_layers=2,
            sequence_length=16,
            use_attention=False,
            use_progressive=False,
            max_resolution=128
        ).to(device)
        
        print("✅ Generator created")
        
        # Create random input sequence
        batch_size = 1
        seq_len = 8
        channels = 3
        height, width = 128, 128
        
        # Create a more interesting input pattern
        input_sequence = torch.zeros(batch_size, seq_len, channels, height, width)
        
        for t in range(seq_len):
            # Create a moving pattern
            frame = torch.zeros(channels, height, width)
            
            # Add some patterns
            center_x = int(width/2 + 30 * np.sin(t * 0.5))
            center_y = int(height/2 + 20 * np.cos(t * 0.5))
            
            # Create circular pattern
            y, x = torch.meshgrid(torch.arange(height), torch.arange(width), indexing='ij')
            dist = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
            
            # Add pattern to each channel
            pattern = torch.exp(-dist / 20) * torch.sin(dist / 5 + t)
            frame[0] = pattern * 0.5 + 0.5  # Red channel
            frame[1] = pattern * 0.3 + 0.5  # Green channel  
            frame[2] = pattern * 0.7 + 0.5  # Blue channel
            
            input_sequence[0, t] = frame
        
        print(f"✅ Input sequence created: {input_sequence.shape}")
        
        # Generate video
        with torch.no_grad():
            generated_video = generator(input_sequence, future_frames=16)
            print(f"✅ Generated video: {generated_video.shape}")
        
        # Save as video file
        output_dir = "outputs/generated_videos"
        os.makedirs(output_dir, exist_ok=True)
        
        # Convert to numpy and save
        generated_np = generated_video.squeeze(0).cpu().numpy()  # Remove batch dimension
        
        # Normalize to 0-255 range
        generated_np = np.clip(generated_np, 0, 1)
        generated_np = (generated_np * 255).astype(np.uint8)
        
        # Save as MP4
        output_path = os.path.join(output_dir, "sample_generated_video.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 10, (width, height))
        
        for frame_idx in range(generated_np.shape[0]):
            frame = generated_np[frame_idx]  # Shape: (3, H, W)
            frame = np.transpose(frame, (1, 2, 0))  # Convert to (H, W, 3)
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out.write(frame_bgr)
        
        out.release()
        print(f"✅ Video saved: {output_path}")
        
        # Also save input sequence for comparison
        input_path = os.path.join(output_dir, "input_sequence.mp4")
        out_input = cv2.VideoWriter(input_path, fourcc, 10, (width, height))
        
        input_np = input_sequence.squeeze(0).cpu().numpy()
        input_np = np.clip(input_np, 0, 1)
        input_np = (input_np * 255).astype(np.uint8)
        
        for frame_idx in range(input_np.shape[0]):
            frame = input_np[frame_idx]
            frame = np.transpose(frame, (1, 2, 0))
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out_input.write(frame_bgr)
        
        out_input.release()
        print(f"✅ Input sequence saved: {input_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_generation_api():
    """Test the web generation API"""
    print("\n🌐 Testing Web Generation API...")
    
    try:
        import requests
        
        # Test if the generation endpoint exists
        response = requests.get("http://localhost:5000/generation")
        if response.status_code == 200:
            print("✅ Generation page accessible")
        else:
            print(f"❌ Generation page not accessible: {response.status_code}")
            return False
        
        # For now, we can't test actual generation without a trained model
        # But we can verify the interface is ready
        print("✅ Generation interface ready")
        return True
        
    except Exception as e:
        print(f"❌ Web API test failed: {e}")
        return False

def create_demo_comparison():
    """Create a demo showing input vs generated frames"""
    print("\n🖼️ Creating Demo Comparison...")
    
    try:
        # Check if we have generated videos
        output_dir = "outputs/generated_videos"
        input_video = os.path.join(output_dir, "input_sequence.mp4")
        generated_video = os.path.join(output_dir, "sample_generated_video.mp4")
        
        if not os.path.exists(input_video) or not os.path.exists(generated_video):
            print("❌ Generated videos not found")
            return False
        
        # Create a side-by-side comparison image
        cap_input = cv2.VideoCapture(input_video)
        cap_generated = cv2.VideoCapture(generated_video)
        
        # Read first frames
        ret1, frame1 = cap_input.read()
        ret2, frame2 = cap_generated.read()
        
        if ret1 and ret2:
            # Create side-by-side comparison
            comparison = np.hstack([frame1, frame2])
            
            # Add labels
            cv2.putText(comparison, "Input Sequence", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(comparison, "AI Generated", (frame1.shape[1] + 10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # Save comparison
            comparison_path = os.path.join(output_dir, "comparison.jpg")
            cv2.imwrite(comparison_path, comparison)
            print(f"✅ Comparison saved: {comparison_path}")
        
        cap_input.release()
        cap_generated.release()
        
        return True
        
    except Exception as e:
        print(f"❌ Demo comparison failed: {e}")
        return False

def main():
    """Run generation tests"""
    print("🎬 VIDEO GENERATION TEST")
    print("=" * 50)
    
    # Test 1: Create sample video
    generation_ok = create_sample_video()
    
    # Test 2: Test web API
    web_api_ok = test_web_generation_api()
    
    # Test 3: Create demo comparison
    comparison_ok = create_demo_comparison()
    
    print("\n" + "=" * 50)
    print("📋 GENERATION TEST RESULTS:")
    print(f"✅ Video Generation: {'Working' if generation_ok else 'Failed'}")
    print(f"✅ Web API: {'Working' if web_api_ok else 'Failed'}")
    print(f"✅ Demo Creation: {'Working' if comparison_ok else 'Failed'}")
    
    if generation_ok:
        print(f"\n🎬 GENERATED VIDEOS:")
        print(f"   📁 Location: outputs/generated_videos/")
        print(f"   🎥 Sample Video: sample_generated_video.mp4")
        print(f"   📊 Input Sequence: input_sequence.mp4")
        print(f"   🖼️ Comparison: comparison.jpg")
        
        print(f"\n🎉 VIDEO GENERATION IS WORKING!")
        print(f"Check the outputs/generated_videos/ folder to see the results.")
    else:
        print(f"\n❌ Video generation needs fixing.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
