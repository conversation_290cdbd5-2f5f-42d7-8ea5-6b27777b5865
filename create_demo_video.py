"""
Create a simple demo video for testing the upload functionality
"""

import cv2
import numpy as np
import os

def create_demo_video():
    """Create a simple demo video with moving shapes"""
    
    # Video parameters
    width, height = 256, 256
    fps = 30
    duration = 5  # seconds
    total_frames = fps * duration
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('demo_video.mp4', fourcc, fps, (width, height))
    
    print(f"Creating demo video: {width}x{height}, {fps} FPS, {duration} seconds...")
    
    for frame_num in range(total_frames):
        # Create a black frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add a moving circle
        center_x = int(width/2 + 80 * np.sin(frame_num * 0.1))
        center_y = int(height/2 + 60 * np.cos(frame_num * 0.1))
        cv2.circle(frame, (center_x, center_y), 30, (0, 255, 255), -1)
        
        # Add a moving rectangle
        rect_x = int(50 + frame_num * 2) % (width - 60)
        rect_y = int(height/2 - 20)
        cv2.rectangle(frame, (rect_x, rect_y), (rect_x + 60, rect_y + 40), (255, 0, 255), -1)
        
        # Add some text
        text = f"Frame {frame_num + 1}"
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Add gradient background
        for y in range(height):
            intensity = int(255 * y / height * 0.3)
            frame[y, :, 2] = np.minimum(frame[y, :, 2] + intensity, 255)
        
        out.write(frame)
    
    out.release()
    print("Demo video created successfully: demo_video.mp4")
    
    # Move to raw_videos directory
    os.makedirs('data/raw_videos', exist_ok=True)
    if os.path.exists('demo_video.mp4'):
        import shutil
        shutil.move('demo_video.mp4', 'data/raw_videos/demo_video.mp4')
        print("Demo video moved to data/raw_videos/")

def create_multiple_demo_videos():
    """Create multiple demo videos with different patterns"""
    
    patterns = [
        ("spiral", "Spiral pattern"),
        ("waves", "Wave pattern"), 
        ("particles", "Particle system"),
        ("geometric", "Geometric shapes")
    ]
    
    os.makedirs('data/raw_videos', exist_ok=True)
    
    for pattern_name, description in patterns:
        print(f"Creating {description}...")
        
        width, height = 256, 256
        fps = 30
        duration = 3
        total_frames = fps * duration
        
        filename = f'data/raw_videos/demo_{pattern_name}.mp4'
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(filename, fourcc, fps, (width, height))
        
        for frame_num in range(total_frames):
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            if pattern_name == "spiral":
                # Spiral pattern
                for i in range(0, 360, 10):
                    angle = np.radians(i + frame_num * 5)
                    radius = 50 + i * 0.3
                    x = int(width/2 + radius * np.cos(angle))
                    y = int(height/2 + radius * np.sin(angle))
                    if 0 <= x < width and 0 <= y < height:
                        cv2.circle(frame, (x, y), 3, (255, 255, 0), -1)
            
            elif pattern_name == "waves":
                # Wave pattern
                for x in range(width):
                    y1 = int(height/2 + 50 * np.sin(x * 0.05 + frame_num * 0.2))
                    y2 = int(height/2 + 30 * np.cos(x * 0.08 + frame_num * 0.15))
                    if 0 <= y1 < height:
                        cv2.circle(frame, (x, y1), 2, (0, 255, 0), -1)
                    if 0 <= y2 < height:
                        cv2.circle(frame, (x, y2), 2, (0, 0, 255), -1)
            
            elif pattern_name == "particles":
                # Particle system
                num_particles = 20
                for i in range(num_particles):
                    x = int(width/2 + 80 * np.sin(frame_num * 0.1 + i * 0.3))
                    y = int(height/2 + 80 * np.cos(frame_num * 0.1 + i * 0.3))
                    size = int(5 + 3 * np.sin(frame_num * 0.2 + i))
                    color = (int(255 * (i % 3 == 0)), int(255 * (i % 3 == 1)), int(255 * (i % 3 == 2)))
                    cv2.circle(frame, (x, y), size, color, -1)
            
            elif pattern_name == "geometric":
                # Geometric shapes
                # Rotating triangle
                center = (width//2, height//2)
                angle = frame_num * 0.1
                points = []
                for i in range(3):
                    x = int(center[0] + 60 * np.cos(angle + i * 2 * np.pi / 3))
                    y = int(center[1] + 60 * np.sin(angle + i * 2 * np.pi / 3))
                    points.append([x, y])
                cv2.fillPoly(frame, [np.array(points)], (255, 100, 100))
                
                # Pulsing square
                size = int(40 + 20 * np.sin(frame_num * 0.2))
                cv2.rectangle(frame, (center[0] - size, center[1] - size), 
                            (center[0] + size, center[1] + size), (100, 255, 100), 3)
            
            out.write(frame)
        
        out.release()
        print(f"Created: {filename}")
    
    print(f"\nCreated {len(patterns)} demo videos in data/raw_videos/")

if __name__ == "__main__":
    print("Creating demo videos for testing...")
    create_multiple_demo_videos()
    print("Demo videos created successfully!")
    print("You can now test the upload functionality in the web interface.")
