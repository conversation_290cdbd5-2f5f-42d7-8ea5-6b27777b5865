 # name="amites<PERSON>r"
 # print("my name is:",name)
 # print(type(name))
 # can=True
 # print("I can code:",can)    

# a=20
# b=30
# sum=a+b
# print("the sum of two numbers is :",sum)
# print(type(sum))

# age=int(input("enter your age"))
# if(age>18):
#     print("you are eligible to vote")
# elif(age==18):
#     print("you are just eligible to vote")
# else:
#     print("you are not eligible to vote")


# a=3
# b=4
# print(a<b and b>a)

# val=int(input("enter a value:"))
# print(type(val),val)


# num1=int(input("enter a number:"))
# num2=int(input("enter a second value:"))
# sum=num1+num2
# print("sum of two numbers is :",sum)


# s=float(input("enter a side length of a square:"))
# area=s*s
# print("area of a square is :", area)


# str="apna college acha hain"
# print(str[5])



# num={2,3,4,5,2,3}

# count=1
# while count<=5:
#     print("hello world")
#     count+=1
# print(count)


# i=100
# while i>=1:
#     print(i)
#     i-=1
# print(i)

# n=3
# i=1
# while i<=10:
#     print(n*i)
#     i+=1


# i=1
# while i<=10:
#     print(i&i)


# def sum(a,b):
#     s=a+b
#     return s
# print(sum(3,5))


# def find_length(li):
#     indx=0
#     while indx<=len(li)
#     print(li)
# li=[12,4,5,45,344,5]
# print(find_length(li))


# def count(n):
#     if(n==-20):
#         return 
#     print(n)
#     count(n-1)
# count(6)

# def fac(n):
#     if(n==0 or n==1):
#        return 1
#     else:
#         return n*fac(n-1)
    
# print(fac(5))


# def main():
#     x=print("hello")
#     print(x)
#     return
# main()


class car:
    def __init__(self,name):
        self.name=name


car1=car()
car1.__init__(lambo)       




