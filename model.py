"""
Video Generation Model Architecture
Based on 3D ConvLSTM and GAN principles
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ConvLSTMCell(nn.Module):
    """3D Convolutional LSTM Cell for video processing"""
    
    def __init__(self, input_dim, hidden_dim, kernel_size, bias=True):
        super(ConvLSTMCell, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size[0] // 2, kernel_size[1] // 2
        self.bias = bias
        
        self.conv = nn.Conv2d(in_channels=self.input_dim + self.hidden_dim,
                              out_channels=4 * self.hidden_dim,
                              kernel_size=self.kernel_size,
                              padding=self.padding,
                              bias=self.bias)
    
    def forward(self, input_tensor, cur_state):
        h_cur, c_cur = cur_state
        
        combined = torch.cat([input_tensor, h_cur], dim=1)
        combined_conv = self.conv(combined)
        
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)
        i = torch.sigmoid(cc_i)
        f = torch.sigmoid(cc_f)
        o = torch.sigmoid(cc_o)
        g = torch.tanh(cc_g)
        
        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next

class VideoGenerator(nn.Module):
    """Video Generator using ConvLSTM"""
    
    def __init__(self, input_channels=3, hidden_dims=[64, 128, 256], 
                 kernel_size=(3, 3), num_layers=3, sequence_length=16):
        super(VideoGenerator, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_dims = hidden_dims
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.sequence_length = sequence_length
        
        # Encoder (downsampling)
        self.encoder_convlstm = nn.ModuleList()
        self.encoder_conv = nn.ModuleList()
        
        for i in range(num_layers):
            input_dim = input_channels if i == 0 else hidden_dims[i-1]
            
            self.encoder_convlstm.append(
                ConvLSTMCell(input_dim=input_dim,
                           hidden_dim=hidden_dims[i],
                           kernel_size=kernel_size)
            )
            
            self.encoder_conv.append(
                nn.Conv2d(hidden_dims[i], hidden_dims[i], 
                         kernel_size=3, stride=2, padding=1)
            )
        
        # Decoder (upsampling)
        self.decoder_convlstm = nn.ModuleList()
        self.decoder_conv = nn.ModuleList()
        
        for i in range(num_layers):
            input_dim = hidden_dims[-(i+1)]
            output_dim = hidden_dims[-(i+2)] if i < num_layers-1 else input_channels
            
            self.decoder_convlstm.append(
                ConvLSTMCell(input_dim=input_dim,
                           hidden_dim=input_dim,
                           kernel_size=kernel_size)
            )
            
            self.decoder_conv.append(
                nn.ConvTranspose2d(input_dim, output_dim,
                                 kernel_size=4, stride=2, padding=1)
            )
        
        # Final output layer
        self.output_conv = nn.Conv2d(input_channels, input_channels, 
                                   kernel_size=3, padding=1)
    
    def forward(self, x, future_frames=8):
        batch_size, seq_len, channels, height, width = x.size()
        
        # Initialize hidden states for encoder
        encoder_states = []
        for i in range(self.num_layers):
            h_size = (batch_size, self.hidden_dims[i], 
                     height // (2**i), width // (2**i))
            h = torch.zeros(h_size, device=x.device)
            c = torch.zeros(h_size, device=x.device)
            encoder_states.append((h, c))
        
        # Encode input sequence
        encoded_features = []
        for t in range(seq_len):
            input_frame = x[:, t]
            layer_input = input_frame
            
            for i in range(self.num_layers):
                h, c = self.encoder_convlstm[i](layer_input, encoder_states[i])
                encoder_states[i] = (h, c)
                layer_input = F.relu(self.encoder_conv[i](h))
            
            encoded_features.append(layer_input)
        
        # Initialize decoder states
        decoder_states = []
        for i in range(self.num_layers):
            h_size = encoder_states[-(i+1)][0].size()
            h = torch.zeros(h_size, device=x.device)
            c = torch.zeros(h_size, device=x.device)
            decoder_states.append((h, c))
        
        # Generate future frames
        generated_frames = []
        decoder_input = encoded_features[-1]  # Use last encoded feature
        
        for t in range(future_frames):
            layer_input = decoder_input
            
            for i in range(self.num_layers):
                h, c = self.decoder_convlstm[i](layer_input, decoder_states[i])
                decoder_states[i] = (h, c)
                layer_input = F.relu(self.decoder_conv[i](h))
            
            # Generate output frame
            output_frame = torch.tanh(self.output_conv(layer_input))
            generated_frames.append(output_frame)
            
            # Use generated frame as input for next iteration
            decoder_input = encoded_features[-1]  # Keep using last encoded feature
        
        return torch.stack(generated_frames, dim=1)

class VideoDiscriminator(nn.Module):
    """Video Discriminator for adversarial training"""
    
    def __init__(self, input_channels=3, hidden_dims=[64, 128, 256, 512]):
        super(VideoDiscriminator, self).__init__()
        
        self.conv3d_layers = nn.ModuleList()
        
        # 3D Convolutional layers for temporal discrimination
        for i, hidden_dim in enumerate(hidden_dims):
            input_dim = input_channels if i == 0 else hidden_dims[i-1]
            
            self.conv3d_layers.append(
                nn.Sequential(
                    nn.Conv3d(input_dim, hidden_dim, 
                             kernel_size=(3, 4, 4), stride=(1, 2, 2), padding=(1, 1, 1)),
                    nn.BatchNorm3d(hidden_dim),
                    nn.LeakyReLU(0.2, inplace=True)
                )
            )
        
        # Final classification layer
        self.classifier = nn.Sequential(
            nn.AdaptiveAvgPool3d((1, 1, 1)),
            nn.Flatten(),
            nn.Linear(hidden_dims[-1], 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x shape: (batch_size, seq_len, channels, height, width)
        # Convert to (batch_size, channels, seq_len, height, width) for 3D conv
        x = x.permute(0, 2, 1, 3, 4)
        
        for conv_layer in self.conv3d_layers:
            x = conv_layer(x)
        
        return self.classifier(x)

def test_models():
    """Test the model architectures"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test data
    batch_size = 2
    seq_len = 8
    channels = 3
    height, width = 64, 64
    
    x = torch.randn(batch_size, seq_len, channels, height, width).to(device)
    
    # Test Generator
    generator = VideoGenerator(input_channels=channels).to(device)
    generated = generator(x, future_frames=4)
    print(f"Generator output shape: {generated.shape}")
    
    # Test Discriminator
    discriminator = VideoDiscriminator(input_channels=channels).to(device)
    disc_output = discriminator(generated)
    print(f"Discriminator output shape: {disc_output.shape}")
    
    print("Model architectures tested successfully!")

if __name__ == "__main__":
    test_models()
