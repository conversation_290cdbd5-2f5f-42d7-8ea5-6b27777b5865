"""
Video processing utilities for AI video generation model
"""

import os
import cv2
import numpy as np
from PIL import Image
import torch
from torchvision import transforms
from tqdm import tqdm
import json

class VideoProcessor:
    def __init__(self, input_dir="data/raw_videos", output_dir="data/processed_videos", 
                 frame_dir="data/frames", target_size=(256, 256), fps=30):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.frame_dir = frame_dir
        self.target_size = target_size
        self.fps = fps
        
        # Create directories if they don't exist
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(frame_dir, exist_ok=True)
        
        # Video preprocessing transforms
        self.transform = transforms.Compose([
            transforms.Resize(target_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def extract_frames(self, video_path, max_frames=None):
        """Extract frames from a video file"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        frame_count = 0
        
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        video_frame_dir = os.path.join(self.frame_dir, video_name)
        os.makedirs(video_frame_dir, exist_ok=True)
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            if max_frames and frame_count >= max_frames:
                break
            
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Resize frame
            frame_resized = cv2.resize(frame_rgb, self.target_size)
            
            # Save frame
            frame_path = os.path.join(video_frame_dir, f"frame_{frame_count:06d}.jpg")
            Image.fromarray(frame_resized).save(frame_path)
            
            frames.append(frame_resized)
            frame_count += 1
        
        cap.release()
        print(f"Extracted {frame_count} frames from {video_name}")
        return frames, video_frame_dir
    
    def preprocess_video(self, video_path, sequence_length=16):
        """Preprocess a single video into training sequences"""
        frames, frame_dir = self.extract_frames(video_path)
        
        if len(frames) < sequence_length:
            print(f"Warning: Video {video_path} has only {len(frames)} frames, skipping...")
            return []
        
        sequences = []
        
        # Create overlapping sequences
        for i in range(0, len(frames) - sequence_length + 1, sequence_length // 2):
            sequence = frames[i:i + sequence_length]
            
            # Convert to tensors
            tensor_sequence = []
            for frame in sequence:
                pil_frame = Image.fromarray(frame)
                tensor_frame = self.transform(pil_frame)
                tensor_sequence.append(tensor_frame)
            
            sequences.append(torch.stack(tensor_sequence))
        
        return sequences
    
    def process_all_videos(self, sequence_length=16):
        """Process all videos in the input directory"""
        if not os.path.exists(self.input_dir):
            print(f"Input directory {self.input_dir} does not exist!")
            return
        
        video_files = [f for f in os.listdir(self.input_dir) 
                      if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        
        if not video_files:
            print(f"No video files found in {self.input_dir}")
            return
        
        all_sequences = []
        metadata = []
        
        for video_file in tqdm(video_files, desc="Processing videos"):
            video_path = os.path.join(self.input_dir, video_file)
            sequences = self.preprocess_video(video_path, sequence_length)
            
            for i, sequence in enumerate(sequences):
                sequence_name = f"{os.path.splitext(video_file)[0]}_seq_{i:03d}"
                sequence_path = os.path.join(self.output_dir, f"{sequence_name}.pt")
                
                # Save sequence tensor
                torch.save(sequence, sequence_path)
                
                metadata.append({
                    'sequence_name': sequence_name,
                    'original_video': video_file,
                    'sequence_index': i,
                    'sequence_length': sequence_length,
                    'shape': list(sequence.shape)
                })
                
                all_sequences.append(sequence)
        
        # Save metadata
        metadata_path = os.path.join(self.output_dir, 'metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"Processed {len(video_files)} videos into {len(all_sequences)} sequences")
        print(f"Metadata saved to {metadata_path}")
        
        return all_sequences, metadata

def main():
    """Main function to process videos"""
    processor = VideoProcessor()
    
    print("Starting video processing...")
    sequences, metadata = processor.process_all_videos()
    
    if sequences:
        print(f"Successfully processed videos!")
        print(f"Total sequences: {len(sequences)}")
        print(f"Sequence shape: {sequences[0].shape}")
    else:
        print("No videos were processed. Please add video files to data/raw_videos/")

if __name__ == "__main__":
    main()
