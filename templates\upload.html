{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Upload Training Videos</h2>
        <p>Upload videos that will be used to train your AI model. Supported formats: MP4, AVI, MOV, MKV</p>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form id="upload-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="video-files" class="form-label">Select Video Files</label>
                        <input type="file" class="form-control" id="video-files" name="files[]" multiple accept=".mp4,.avi,.mov,.mkv">
                        <div class="form-text">You can select multiple video files at once.</div>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload Videos</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Upload Tips</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li>• Use high-quality videos</li>
                    <li>• Similar content works best</li>
                    <li>• At least 10-20 videos recommended</li>
                    <li>• Videos should be at least 5 seconds long</li>
                    <li>• Max file size: 500MB per video</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Upload Progress</h5>
            </div>
            <div class="card-body">
                <div id="upload-status">
                    <p>No uploads in progress</p>
                </div>
                <div class="progress" style="display: none;" id="upload-progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="upload-results" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('upload-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('video-files');
    const files = fileInput.files;
    
    if (files.length === 0) {
        alert('Please select at least one video file.');
        return;
    }
    
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
        formData.append('files[]', files[i]);
    }
    
    const statusDiv = document.getElementById('upload-status');
    const progressBar = document.getElementById('upload-progress');
    const progressBarInner = progressBar.querySelector('.progress-bar');
    const resultsDiv = document.getElementById('upload-results');
    
    statusDiv.innerHTML = '<p>Uploading ' + files.length + ' files...</p>';
    progressBar.style.display = 'block';
    progressBarInner.style.width = '0%';
    resultsDiv.innerHTML = '';
    
    // Simulate progress (since we can't get real upload progress easily)
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 90) progress = 90;
        progressBarInner.style.width = progress + '%';
    }, 500);
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBarInner.style.width = '100%';
        
        if (data.success) {
            statusDiv.innerHTML = '<p class="text-success">' + data.message + '</p>';
            
            let resultsHtml = '<h6>Uploaded Files:</h6><ul class="list-group">';
            data.uploaded_files.forEach(file => {
                resultsHtml += '<li class="list-group-item">' + file + '</li>';
            });
            resultsHtml += '</ul>';
            
            resultsDiv.innerHTML = resultsHtml;
            
            // Reset form
            fileInput.value = '';
            
            setTimeout(() => {
                progressBar.style.display = 'none';
            }, 2000);
        } else {
            statusDiv.innerHTML = '<p class="text-danger">Error: ' + data.error + '</p>';
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        statusDiv.innerHTML = '<p class="text-danger">Upload failed: ' + error.message + '</p>';
    });
});
</script>
{% endblock %}
