"""
Video Generation Interface
Generate new videos using trained model
"""

import os
import torch
import cv2
import numpy as np
from PIL import Image
import argparse
from model import VideoGenerator
from video_processor import VideoProcessor
import json

class VideoGeneratorInterface:
    """Interface for generating videos using trained model"""
    
    def __init__(self, model_path, device=None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load trained model
        self.generator = VideoGenerator(input_channels=3)
        self.load_model(model_path)
        self.generator.to(self.device)
        self.generator.eval()
        
        # Video processor for preprocessing
        self.processor = VideoProcessor()
        
        print(f"Video generator loaded on {self.device}")
    
    def load_model(self, model_path):
        """Load trained model weights"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'generator_state_dict' in checkpoint:
            # Loading from training checkpoint
            self.generator.load_state_dict(checkpoint['generator_state_dict'])
            print(f"Loaded model from epoch {checkpoint.get('epoch', 'unknown')}")
        else:
            # Loading direct state dict
            self.generator.load_state_dict(checkpoint)
            print("Loaded model weights")
    
    def preprocess_seed_video(self, video_path, sequence_length=8):
        """Preprocess seed video for generation"""
        frames, _ = self.processor.extract_frames(video_path, max_frames=sequence_length)
        
        if len(frames) < sequence_length:
            # Repeat frames if not enough
            while len(frames) < sequence_length:
                frames.extend(frames[:sequence_length - len(frames)])
        
        # Convert to tensor
        tensor_frames = []
        for frame in frames[:sequence_length]:
            pil_frame = Image.fromarray(frame)
            tensor_frame = self.processor.transform(pil_frame)
            tensor_frames.append(tensor_frame)
        
        # Add batch dimension
        sequence_tensor = torch.stack(tensor_frames).unsqueeze(0)
        return sequence_tensor
    
    def generate_from_seed(self, seed_video_path, num_future_frames=16, output_path=None):
        """Generate video continuation from seed video"""
        print(f"Generating video from seed: {seed_video_path}")
        
        # Preprocess seed video
        seed_sequence = self.preprocess_seed_video(seed_video_path)
        seed_sequence = seed_sequence.to(self.device)
        
        # Generate future frames
        with torch.no_grad():
            generated_frames = self.generator(seed_sequence, future_frames=num_future_frames)
        
        # Convert to numpy and denormalize
        generated_frames = generated_frames.squeeze(0).cpu()
        
        # Denormalize frames
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        
        denormalized_frames = []
        for frame in generated_frames:
            denorm_frame = frame * std + mean
            denorm_frame = torch.clamp(denorm_frame, 0, 1)
            denorm_frame = (denorm_frame * 255).permute(1, 2, 0).numpy().astype(np.uint8)
            denormalized_frames.append(denorm_frame)
        
        # Save as video
        if output_path is None:
            output_path = f"outputs/generated_videos/generated_{len(os.listdir('outputs/generated_videos')) + 1:03d}.mp4"
        
        self.save_frames_as_video(denormalized_frames, output_path)
        print(f"Generated video saved: {output_path}")
        
        return output_path
    
    def generate_random_video(self, num_frames=16, output_path=None):
        """Generate video from random noise"""
        print("Generating video from random input...")
        
        # Create random input sequence
        batch_size = 1
        seq_len = 8
        channels = 3
        height, width = 256, 256
        
        random_input = torch.randn(batch_size, seq_len, channels, height, width)
        random_input = random_input.to(self.device)
        
        # Generate frames
        with torch.no_grad():
            generated_frames = self.generator(random_input, future_frames=num_frames)
        
        # Process and save
        generated_frames = generated_frames.squeeze(0).cpu()
        
        # Denormalize
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        
        denormalized_frames = []
        for frame in generated_frames:
            denorm_frame = frame * std + mean
            denorm_frame = torch.clamp(denorm_frame, 0, 1)
            denorm_frame = (denorm_frame * 255).permute(1, 2, 0).numpy().astype(np.uint8)
            denormalized_frames.append(denorm_frame)
        
        if output_path is None:
            output_path = f"outputs/generated_videos/random_generated_{len(os.listdir('outputs/generated_videos')) + 1:03d}.mp4"
        
        self.save_frames_as_video(denormalized_frames, output_path)
        print(f"Random generated video saved: {output_path}")
        
        return output_path
    
    def save_frames_as_video(self, frames, output_path, fps=30):
        """Save frames as video file"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        for frame in frames:
            # Convert RGB to BGR for OpenCV
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out.write(frame_bgr)
        
        out.release()
    
    def interpolate_videos(self, video1_path, video2_path, num_interpolation_steps=8, output_path=None):
        """Interpolate between two videos"""
        print(f"Interpolating between {video1_path} and {video2_path}")
        
        # Preprocess both videos
        seq1 = self.preprocess_seed_video(video1_path).to(self.device)
        seq2 = self.preprocess_seed_video(video2_path).to(self.device)
        
        interpolated_videos = []
        
        # Generate interpolation steps
        for i in range(num_interpolation_steps + 1):
            alpha = i / num_interpolation_steps
            interpolated_input = (1 - alpha) * seq1 + alpha * seq2
            
            with torch.no_grad():
                generated_frames = self.generator(interpolated_input, future_frames=8)
            
            interpolated_videos.append(generated_frames)
        
        # Combine all interpolated videos
        all_frames = []
        for video_tensor in interpolated_videos:
            frames = video_tensor.squeeze(0).cpu()
            
            # Denormalize
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
            
            for frame in frames:
                denorm_frame = frame * std + mean
                denorm_frame = torch.clamp(denorm_frame, 0, 1)
                denorm_frame = (denorm_frame * 255).permute(1, 2, 0).numpy().astype(np.uint8)
                all_frames.append(denorm_frame)
        
        if output_path is None:
            output_path = f"outputs/generated_videos/interpolated_{len(os.listdir('outputs/generated_videos')) + 1:03d}.mp4"
        
        self.save_frames_as_video(all_frames, output_path)
        print(f"Interpolated video saved: {output_path}")
        
        return output_path

def main():
    """Main function for command line interface"""
    parser = argparse.ArgumentParser(description='Generate videos using trained model')
    parser.add_argument('--model', required=True, help='Path to trained model checkpoint')
    parser.add_argument('--mode', choices=['seed', 'random', 'interpolate'], default='random',
                       help='Generation mode')
    parser.add_argument('--seed_video', help='Path to seed video (for seed mode)')
    parser.add_argument('--video1', help='First video for interpolation')
    parser.add_argument('--video2', help='Second video for interpolation')
    parser.add_argument('--frames', type=int, default=16, help='Number of frames to generate')
    parser.add_argument('--output', help='Output video path')
    
    args = parser.parse_args()
    
    # Create generator interface
    generator = VideoGeneratorInterface(args.model)
    
    # Generate based on mode
    if args.mode == 'seed':
        if not args.seed_video:
            print("Error: --seed_video required for seed mode")
            return
        generator.generate_from_seed(args.seed_video, args.frames, args.output)
    
    elif args.mode == 'random':
        generator.generate_random_video(args.frames, args.output)
    
    elif args.mode == 'interpolate':
        if not args.video1 or not args.video2:
            print("Error: --video1 and --video2 required for interpolate mode")
            return
        generator.interpolate_videos(args.video1, args.video2, args.frames, args.output)

if __name__ == "__main__":
    main()
