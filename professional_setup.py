"""
Professional Setup and Optimization System
Complete setup, optimization, and deployment for Video AI Generator
"""

import os
import sys
import subprocess
import torch
import json
import shutil
from pathlib import Path
import platform
import psutil
import time

class ProfessionalSetup:
    """Professional setup and optimization system"""
    
    def __init__(self):
        self.system_info = self.detect_system()
        self.requirements_installed = False
        
    def detect_system(self):
        """Detect system specifications"""
        info = {
            "os": platform.system(),
            "cpu_count": os.cpu_count(),
            "ram_gb": round(psutil.virtual_memory().total / (1024**3), 1),
            "python_version": sys.version,
            "cuda_available": torch.cuda.is_available(),
            "gpu_info": None
        }
        
        if torch.cuda.is_available():
            info["gpu_info"] = {
                "name": torch.cuda.get_device_name(0),
                "memory_gb": round(torch.cuda.get_device_properties(0).total_memory / (1024**3), 1),
                "compute_capability": torch.cuda.get_device_capability(0)
            }
        
        return info
    
    def install_professional_requirements(self):
        """Install all professional requirements with optimizations"""
        print("🔧 Installing Professional Video AI Generator Requirements...")
        print("=" * 60)
        
        # Core requirements with specific versions for stability
        professional_requirements = [
            # Deep Learning Framework
            "torch>=2.0.0",
            "torchvision>=0.15.0",
            "torchaudio>=2.0.0",
            
            # Video Processing (Professional)
            "opencv-python>=4.8.0",
            "imageio[ffmpeg]>=2.31.0",
            "moviepy>=1.0.3",
            "av>=10.0.0",  # Professional video codec support
            
            # Image Processing (Advanced)
            "Pillow>=10.0.0",
            "scikit-image>=0.21.0",
            "albumentations>=1.3.0",
            
            # Scientific Computing (Optimized)
            "numpy>=1.24.0",
            "scipy>=1.11.0",
            "pandas>=2.0.0",
            
            # Visualization (Professional)
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "plotly>=5.15.0",
            
            # Machine Learning (Advanced)
            "scikit-learn>=1.3.0",
            "tensorboard>=2.13.0",
            "wandb>=0.15.0",
            
            # Web Framework (Production)
            "flask>=2.3.0",
            "flask-cors>=4.0.0",
            "flask-socketio>=5.3.0",  # Real-time updates
            "gunicorn>=21.2.0",  # Production server
            
            # System Monitoring
            "psutil>=5.9.0",
            "GPUtil>=1.4.0",
            "nvidia-ml-py3>=7.352.0",
            
            # Performance Optimization
            "numba>=0.57.0",  # JIT compilation
            "cupy-cuda11x>=12.0.0; sys_platform != 'darwin'",  # GPU acceleration
            
            # Development Tools
            "jupyter>=1.0.0",
            "ipython>=8.14.0",
            "tqdm>=4.65.0",
            
            # File Handling (Advanced)
            "h5py>=3.9.0",
            "zarr>=2.16.0",  # Chunked arrays
            "lmdb>=1.4.0",  # Fast database
            
            # Model Optimization
            "onnx>=1.14.0",
            "onnxruntime-gpu>=1.15.0; sys_platform != 'darwin'",
            "tensorrt>=8.6.0; sys_platform == 'linux'",  # NVIDIA optimization
            
            # Audio Processing (for video)
            "librosa>=0.10.0",
            "soundfile>=0.12.0"
        ]
        
        # Install with optimizations
        for requirement in professional_requirements:
            try:
                print(f"Installing {requirement}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    requirement, "--upgrade", "--no-cache-dir"
                ])
            except subprocess.CalledProcessError as e:
                print(f"Warning: Failed to install {requirement}: {e}")
                continue
        
        self.requirements_installed = True
        print("✅ Professional requirements installed successfully!")
    
    def optimize_pytorch(self):
        """Optimize PyTorch for maximum performance"""
        print("\n🚀 Optimizing PyTorch for Maximum Performance...")
        
        # Set optimal environment variables
        optimizations = {
            "OMP_NUM_THREADS": str(self.system_info["cpu_count"]),
            "MKL_NUM_THREADS": str(self.system_info["cpu_count"]),
            "CUDA_LAUNCH_BLOCKING": "0",  # Async CUDA operations
            "TORCH_CUDNN_V8_API_ENABLED": "1",  # Enable cuDNN v8
            "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:128",  # Memory optimization
        }
        
        # Apply optimizations
        for key, value in optimizations.items():
            os.environ[key] = value
            print(f"Set {key}={value}")
        
        # Test PyTorch optimization
        if torch.cuda.is_available():
            print(f"✅ CUDA optimized for {torch.cuda.get_device_name(0)}")
            print(f"✅ cuDNN version: {torch.backends.cudnn.version()}")
            print(f"✅ Memory allocated: {torch.cuda.memory_allocated(0) / 1e9:.2f}GB")
    
    def create_professional_structure(self):
        """Create professional directory structure"""
        print("\n📁 Creating Professional Directory Structure...")
        
        directories = [
            # Data directories
            "data/raw_videos",
            "data/processed_videos",
            "data/frames",
            "data/datasets",
            "data/cache",
            
            # Model directories
            "models/checkpoints",
            "models/trained",
            "models/exported",
            "models/quantized",
            
            # Output directories
            "outputs/generated_videos",
            "outputs/comparisons",
            "outputs/evaluations",
            
            # Configuration
            "configs",
            "configs/presets",
            
            # Logs and monitoring
            "logs/training",
            "logs/system",
            "logs/experiments",
            
            # Web interface
            "static/uploads",
            "static/generated",
            "static/assets",
            "templates/components",
            
            # Scripts and utilities
            "scripts",
            "utils",
            
            # Documentation
            "docs",
            "examples",
            
            # Deployment
            "deployment",
            "deployment/docker",
            "deployment/cloud"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created: {directory}")
    
    def create_optimization_configs(self):
        """Create optimization configurations for different scenarios"""
        print("\n⚙️ Creating Optimization Configurations...")
        
        from config import ConfigManager
        
        # Create optimized configs for different use cases
        scenarios = {
            "development": {
                "description": "Fast iteration for development",
                "batch_size": 2,
                "num_epochs": 10,
                "resolution": 128,
                "use_mixed_precision": True
            },
            "production_cpu": {
                "description": "CPU-only production deployment",
                "batch_size": 1,
                "num_epochs": 50,
                "resolution": 64,
                "use_mixed_precision": False,
                "cpu_optimization": True
            },
            "production_gpu": {
                "description": "GPU-accelerated production",
                "batch_size": 8,
                "num_epochs": 200,
                "resolution": 512,
                "use_mixed_precision": True,
                "tensorrt_optimization": True
            },
            "memory_constrained": {
                "description": "Low memory systems",
                "batch_size": 1,
                "gradient_checkpointing": True,
                "cpu_offload": True,
                "resolution": 64
            },
            "high_quality": {
                "description": "Maximum quality output",
                "batch_size": 4,
                "num_epochs": 500,
                "resolution": 1024,
                "perceptual_weight": 20.0,
                "use_progressive": True
            }
        }
        
        config_manager = ConfigManager()
        
        for scenario, settings in scenarios.items():
            # Apply scenario-specific settings
            for key, value in settings.items():
                if key != "description":
                    # Apply to appropriate config section
                    if hasattr(config_manager.training_config, key):
                        setattr(config_manager.training_config, key, value)
                    elif hasattr(config_manager.model_config, key):
                        setattr(config_manager.model_config, key, value)
                    elif hasattr(config_manager.system_config, key):
                        setattr(config_manager.system_config, key, value)
            
            # Save configuration
            config_path = f"configs/presets/{scenario}_config.json"
            config_manager.save_config(config_path)
            print(f"✅ Created {scenario} config: {settings['description']}")
    
    def create_deployment_scripts(self):
        """Create deployment and utility scripts"""
        print("\n🚀 Creating Deployment Scripts...")
        
        # Professional launcher script
        launcher_script = '''#!/usr/bin/env python3
"""
Professional Video AI Generator Launcher
Automatically detects optimal configuration and starts the system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ConfigManager
from professional_web_app import app, app_state
import argparse

def main():
    parser = argparse.ArgumentParser(description='Professional Video AI Generator')
    parser.add_argument('--config', help='Configuration preset', 
                       choices=['development', 'production_cpu', 'production_gpu', 
                               'memory_constrained', 'high_quality'], 
                       default='auto')
    parser.add_argument('--port', type=int, default=5000, help='Port number')
    parser.add_argument('--host', default='0.0.0.0', help='Host address')
    parser.add_argument('--debug', action='store_true', help='Debug mode')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config != 'auto':
        config_path = f"configs/presets/{args.config}_config.json"
        if os.path.exists(config_path):
            app_state.config_manager.load_config(config_path)
            print(f"Loaded {args.config} configuration")
    
    # Print system info
    app_state.config_manager.print_config()
    
    # Start application
    print(f"\\n🚀 Starting Professional Video AI Generator on {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug, threaded=True)

if __name__ == "__main__":
    main()
'''
        
        with open('launch_professional.py', 'w') as f:
            f.write(launcher_script)
        
        # Make executable on Unix systems
        if platform.system() != 'Windows':
            os.chmod('launch_professional.py', 0o755)
        
        print("✅ Created professional launcher script")
    
    def run_system_benchmark(self):
        """Run comprehensive system benchmark"""
        print("\n🔬 Running System Benchmark...")
        
        benchmark_results = {
            "system_info": self.system_info,
            "pytorch_performance": {},
            "memory_performance": {},
            "disk_performance": {}
        }
        
        # PyTorch benchmark
        if torch.cuda.is_available():
            print("Testing GPU performance...")
            device = torch.device('cuda')
            
            # Matrix multiplication benchmark
            start_time = time.time()
            a = torch.randn(1000, 1000, device=device)
            b = torch.randn(1000, 1000, device=device)
            for _ in range(100):
                c = torch.mm(a, b)
            torch.cuda.synchronize()
            gpu_time = time.time() - start_time
            
            benchmark_results["pytorch_performance"]["gpu_matmul_time"] = gpu_time
            print(f"GPU Matrix Multiplication: {gpu_time:.3f}s")
        
        # Save benchmark results
        with open('logs/system_benchmark.json', 'w') as f:
            json.dump(benchmark_results, f, indent=2)
        
        print("✅ Benchmark completed and saved to logs/system_benchmark.json")
        return benchmark_results
    
    def setup_complete(self):
        """Complete professional setup"""
        print("\n" + "="*60)
        print("🎉 PROFESSIONAL VIDEO AI GENERATOR SETUP COMPLETE!")
        print("="*60)
        
        print(f"\n📊 SYSTEM SUMMARY:")
        print(f"  OS: {self.system_info['os']}")
        print(f"  CPU Cores: {self.system_info['cpu_count']}")
        print(f"  RAM: {self.system_info['ram_gb']}GB")
        
        if self.system_info['cuda_available']:
            gpu_info = self.system_info['gpu_info']
            print(f"  GPU: {gpu_info['name']}")
            print(f"  GPU Memory: {gpu_info['memory_gb']}GB")
        else:
            print("  GPU: Not available (CPU-only mode)")
        
        print(f"\n🚀 QUICK START:")
        print(f"  1. Launch: python launch_professional.py")
        print(f"  2. Open: http://localhost:5000")
        print(f"  3. Upload videos and start training!")
        
        print(f"\n📁 IMPORTANT DIRECTORIES:")
        print(f"  Training Data: data/raw_videos/")
        print(f"  Models: models/checkpoints/")
        print(f"  Generated Videos: outputs/generated_videos/")
        print(f"  Configurations: configs/presets/")
        
        print(f"\n⚙️ AVAILABLE CONFIGURATIONS:")
        print(f"  - development: Fast iteration")
        print(f"  - production_gpu: Maximum performance")
        print(f"  - memory_constrained: Low memory systems")
        print(f"  - high_quality: Best output quality")
        
        print("="*60)

def main():
    """Main setup function"""
    print("🚀 Professional Video AI Generator Setup")
    print("Setting up the most advanced video generation system...")
    
    setup = ProfessionalSetup()
    
    # Run complete setup
    setup.install_professional_requirements()
    setup.optimize_pytorch()
    setup.create_professional_structure()
    setup.create_optimization_configs()
    setup.create_deployment_scripts()
    setup.run_system_benchmark()
    setup.setup_complete()

if __name__ == "__main__":
    main()
