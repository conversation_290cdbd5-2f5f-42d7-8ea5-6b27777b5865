"""
Training script for Video Generation Model
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
from model import VideoGenerator, VideoDiscriminator
import numpy as np

class VideoDataset(Dataset):
    """Dataset class for loading preprocessed video sequences"""
    
    def __init__(self, data_dir="data/processed_videos", sequence_length=16):
        self.data_dir = data_dir
        self.sequence_length = sequence_length
        
        # Load metadata
        metadata_path = os.path.join(data_dir, 'metadata.json')
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                self.metadata = json.load(f)
        else:
            print("No metadata found. Please run video_processor.py first.")
            self.metadata = []
        
        # Filter sequences by length
        self.valid_sequences = [
            item for item in self.metadata 
            if item['sequence_length'] >= sequence_length
        ]
        
        print(f"Found {len(self.valid_sequences)} valid sequences")
    
    def __len__(self):
        return len(self.valid_sequences)
    
    def __getitem__(self, idx):
        sequence_info = self.valid_sequences[idx]
        sequence_path = os.path.join(self.data_dir, f"{sequence_info['sequence_name']}.pt")
        
        # Load sequence tensor
        sequence = torch.load(sequence_path)
        
        # If sequence is longer than needed, take a random crop
        if sequence.shape[0] > self.sequence_length:
            start_idx = torch.randint(0, sequence.shape[0] - self.sequence_length + 1, (1,)).item()
            sequence = sequence[start_idx:start_idx + self.sequence_length]
        
        return sequence

class VideoGANTrainer:
    """Trainer class for Video GAN"""
    
    def __init__(self, generator, discriminator, device, lr_g=0.0002, lr_d=0.0002):
        self.generator = generator.to(device)
        self.discriminator = discriminator.to(device)
        self.device = device
        
        # Optimizers
        self.optimizer_g = optim.Adam(self.generator.parameters(), lr=lr_g, betas=(0.5, 0.999))
        self.optimizer_d = optim.Adam(self.discriminator.parameters(), lr=lr_d, betas=(0.5, 0.999))
        
        # Loss functions
        self.adversarial_loss = nn.BCELoss()
        self.reconstruction_loss = nn.MSELoss()
        
        # Training history
        self.history = {
            'g_loss': [],
            'd_loss': [],
            'reconstruction_loss': []
        }
    
    def train_step(self, real_videos):
        batch_size = real_videos.size(0)
        
        # Split videos into input and target
        input_frames = 8
        target_frames = real_videos.size(1) - input_frames
        
        input_sequence = real_videos[:, :input_frames]
        target_sequence = real_videos[:, input_frames:]
        
        # Generate fake videos
        fake_videos = self.generator(input_sequence, future_frames=target_frames)
        
        # Train Discriminator
        self.optimizer_d.zero_grad()
        
        # Real videos
        real_labels = torch.ones(batch_size, 1, device=self.device)
        real_output = self.discriminator(target_sequence)
        d_loss_real = self.adversarial_loss(real_output, real_labels)
        
        # Fake videos
        fake_labels = torch.zeros(batch_size, 1, device=self.device)
        fake_output = self.discriminator(fake_videos.detach())
        d_loss_fake = self.adversarial_loss(fake_output, fake_labels)
        
        d_loss = (d_loss_real + d_loss_fake) / 2
        d_loss.backward()
        self.optimizer_d.step()
        
        # Train Generator
        self.optimizer_g.zero_grad()
        
        # Adversarial loss
        fake_output = self.discriminator(fake_videos)
        g_loss_adv = self.adversarial_loss(fake_output, real_labels)
        
        # Reconstruction loss
        g_loss_recon = self.reconstruction_loss(fake_videos, target_sequence)
        
        # Combined generator loss
        g_loss = g_loss_adv + 10.0 * g_loss_recon  # Weight reconstruction loss higher
        g_loss.backward()
        self.optimizer_g.step()
        
        return {
            'g_loss': g_loss.item(),
            'd_loss': d_loss.item(),
            'reconstruction_loss': g_loss_recon.item()
        }
    
    def train(self, dataloader, epochs=100, save_interval=10):
        """Main training loop"""
        print(f"Starting training for {epochs} epochs...")
        
        for epoch in range(epochs):
            epoch_losses = {'g_loss': [], 'd_loss': [], 'reconstruction_loss': []}
            
            progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")
            
            for batch_idx, real_videos in enumerate(progress_bar):
                real_videos = real_videos.to(self.device)
                
                # Skip if batch is too small
                if real_videos.size(1) < 16:
                    continue
                
                losses = self.train_step(real_videos)
                
                # Update progress bar
                progress_bar.set_postfix({
                    'G_Loss': f"{losses['g_loss']:.4f}",
                    'D_Loss': f"{losses['d_loss']:.4f}",
                    'Recon': f"{losses['reconstruction_loss']:.4f}"
                })
                
                # Collect losses
                for key in epoch_losses:
                    epoch_losses[key].append(losses[key])
            
            # Average losses for the epoch
            for key in self.history:
                if epoch_losses[key]:
                    avg_loss = np.mean(epoch_losses[key])
                    self.history[key].append(avg_loss)
            
            # Print epoch summary
            if self.history['g_loss']:
                print(f"Epoch {epoch+1} - G_Loss: {self.history['g_loss'][-1]:.4f}, "
                      f"D_Loss: {self.history['d_loss'][-1]:.4f}, "
                      f"Recon: {self.history['reconstruction_loss'][-1]:.4f}")
            
            # Save model checkpoint
            if (epoch + 1) % save_interval == 0:
                self.save_checkpoint(epoch + 1)
        
        print("Training completed!")
    
    def save_checkpoint(self, epoch):
        """Save model checkpoint"""
        checkpoint_dir = "models/checkpoints"
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'optimizer_g_state_dict': self.optimizer_g.state_dict(),
            'optimizer_d_state_dict': self.optimizer_d.state_dict(),
            'history': self.history
        }
        
        checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        print(f"Checkpoint saved: {checkpoint_path}")
    
    def plot_losses(self):
        """Plot training losses"""
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 3, 1)
        plt.plot(self.history['g_loss'])
        plt.title('Generator Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        
        plt.subplot(1, 3, 2)
        plt.plot(self.history['d_loss'])
        plt.title('Discriminator Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        
        plt.subplot(1, 3, 3)
        plt.plot(self.history['reconstruction_loss'])
        plt.title('Reconstruction Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        
        plt.tight_layout()
        plt.savefig('logs/training_losses.png')
        plt.show()

def main():
    """Main training function"""
    # Check if processed data exists
    if not os.path.exists("data/processed_videos/metadata.json"):
        print("No processed video data found!")
        print("Please run: python video_processor.py")
        return
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create dataset and dataloader
    dataset = VideoDataset(sequence_length=16)
    if len(dataset) == 0:
        print("No valid sequences found. Please add videos and run video_processor.py")
        return
    
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=2)
    
    # Create models
    generator = VideoGenerator(input_channels=3, sequence_length=16)
    discriminator = VideoDiscriminator(input_channels=3)
    
    # Create trainer
    trainer = VideoGANTrainer(generator, discriminator, device)
    
    # Start training
    trainer.train(dataloader, epochs=50, save_interval=5)
    
    # Plot results
    trainer.plot_losses()

if __name__ == "__main__":
    main()
