"""
Web interface for Video AI Generator
"""

from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime
import threading
from video_processor import VideoProcessor
from generate_video import VideoGeneratorInterface
import torch

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# Global variables
video_processor = None
video_generator = None
training_status = {"is_training": False, "progress": 0, "message": ""}

# Allowed file extensions
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/upload', methods=['GET', 'POST'])
def upload_videos():
    """Upload training videos"""
    if request.method == 'POST':
        if 'files[]' not in request.files:
            return jsonify({'error': 'No files selected'})
        
        files = request.files.getlist('files[]')
        uploaded_files = []
        
        for file in files:
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # Add timestamp to avoid conflicts
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                
                filepath = os.path.join('data/raw_videos', filename)
                os.makedirs('data/raw_videos', exist_ok=True)
                file.save(filepath)
                uploaded_files.append(filename)
        
        return jsonify({
            'success': True,
            'uploaded_files': uploaded_files,
            'message': f'Uploaded {len(uploaded_files)} videos successfully'
        })
    
    return render_template('upload.html')

@app.route('/process_videos', methods=['POST'])
def process_videos():
    """Process uploaded videos"""
    global video_processor
    
    try:
        video_processor = VideoProcessor()
        sequences, metadata = video_processor.process_all_videos()
        
        return jsonify({
            'success': True,
            'sequences_count': len(sequences) if sequences else 0,
            'message': 'Videos processed successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/train', methods=['POST'])
def start_training():
    """Start model training"""
    global training_status
    
    if training_status["is_training"]:
        return jsonify({
            'success': False,
            'error': 'Training is already in progress'
        })
    
    # Get training parameters
    epochs = int(request.form.get('epochs', 50))
    batch_size = int(request.form.get('batch_size', 4))
    
    # Start training in background thread
    training_thread = threading.Thread(
        target=run_training,
        args=(epochs, batch_size)
    )
    training_thread.daemon = True
    training_thread.start()
    
    return jsonify({
        'success': True,
        'message': 'Training started'
    })

def run_training(epochs, batch_size):
    """Run training in background"""
    global training_status
    
    try:
        training_status["is_training"] = True
        training_status["progress"] = 0
        training_status["message"] = "Initializing training..."
        
        # Import here to avoid circular imports
        from train_model import VideoDataset, VideoGANTrainer
        from model import VideoGenerator, VideoDiscriminator
        from torch.utils.data import DataLoader
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Create dataset
        training_status["message"] = "Loading dataset..."
        dataset = VideoDataset(sequence_length=16)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=2)
        
        # Create models
        training_status["message"] = "Creating models..."
        generator = VideoGenerator(input_channels=3, sequence_length=16)
        discriminator = VideoDiscriminator(input_channels=3)
        
        # Create trainer
        trainer = VideoGANTrainer(generator, discriminator, device)
        
        # Custom training loop with progress updates
        training_status["message"] = "Training started..."
        
        for epoch in range(epochs):
            training_status["progress"] = int((epoch / epochs) * 100)
            training_status["message"] = f"Training epoch {epoch+1}/{epochs}"
            
            epoch_losses = {'g_loss': [], 'd_loss': [], 'reconstruction_loss': []}
            
            for batch_idx, real_videos in enumerate(dataloader):
                real_videos = real_videos.to(device)
                
                if real_videos.size(1) < 16:
                    continue
                
                losses = trainer.train_step(real_videos)
                
                for key in epoch_losses:
                    epoch_losses[key].append(losses[key])
            
            # Save checkpoint every 10 epochs
            if (epoch + 1) % 10 == 0:
                trainer.save_checkpoint(epoch + 1)
        
        training_status["progress"] = 100
        training_status["message"] = "Training completed successfully!"
        
    except Exception as e:
        training_status["message"] = f"Training failed: {str(e)}"
    finally:
        training_status["is_training"] = False

@app.route('/training_status')
def get_training_status():
    """Get current training status"""
    return jsonify(training_status)

@app.route('/generate', methods=['GET', 'POST'])
def generate_video():
    """Generate new videos"""
    if request.method == 'POST':
        try:
            # Get latest model checkpoint
            checkpoint_dir = "models/checkpoints"
            if not os.path.exists(checkpoint_dir):
                return jsonify({
                    'success': False,
                    'error': 'No trained model found. Please train a model first.'
                })
            
            checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
            if not checkpoints:
                return jsonify({
                    'success': False,
                    'error': 'No model checkpoints found.'
                })
            
            # Use latest checkpoint
            latest_checkpoint = sorted(checkpoints)[-1]
            model_path = os.path.join(checkpoint_dir, latest_checkpoint)
            
            # Initialize generator
            global video_generator
            video_generator = VideoGeneratorInterface(model_path)
            
            # Get generation parameters
            mode = request.form.get('mode', 'random')
            num_frames = int(request.form.get('frames', 16))
            
            if mode == 'random':
                output_path = video_generator.generate_random_video(num_frames)
            elif mode == 'seed':
                if 'seed_video' not in request.files:
                    return jsonify({
                        'success': False,
                        'error': 'No seed video provided'
                    })
                
                seed_file = request.files['seed_video']
                if seed_file.filename == '':
                    return jsonify({
                        'success': False,
                        'error': 'No seed video selected'
                    })
                
                # Save seed video temporarily
                seed_filename = secure_filename(seed_file.filename)
                seed_path = os.path.join(app.config['UPLOAD_FOLDER'], seed_filename)
                os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
                seed_file.save(seed_path)
                
                output_path = video_generator.generate_from_seed(seed_path, num_frames)
            
            # Return relative path for web serving
            relative_path = output_path.replace('\\', '/')
            
            return jsonify({
                'success': True,
                'video_path': relative_path,
                'message': 'Video generated successfully!'
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            })
    
    return render_template('generate.html')

@app.route('/models')
def list_models():
    """List available trained models"""
    checkpoint_dir = "models/checkpoints"
    models = []
    
    if os.path.exists(checkpoint_dir):
        for filename in os.listdir(checkpoint_dir):
            if filename.endswith('.pth'):
                filepath = os.path.join(checkpoint_dir, filename)
                stat = os.stat(filepath)
                models.append({
                    'filename': filename,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
    
    return jsonify(models)

@app.route('/download/<path:filename>')
def download_file(filename):
    """Download generated videos"""
    return send_file(filename, as_attachment=True)

# Create templates directory and basic HTML templates
def create_templates():
    """Create basic HTML templates"""
    os.makedirs('templates', exist_ok=True)
    
    # Base template
    base_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video AI Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Video AI Generator</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/upload">Upload</a>
                <a class="nav-link" href="/generate">Generate</a>
            </div>
        </div>
    </nav>
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    with open('templates/base.html', 'w') as f:
        f.write(base_template)

if __name__ == '__main__':
    create_templates()
    app.run(debug=True, host='0.0.0.0', port=5000)
