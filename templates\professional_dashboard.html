{% extends "professional_base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt text-primary me-2"></i>
            Professional Video AI Dashboard
        </h1>
        <p class="text-muted">State-of-the-art video generation system</p>
    </div>
</div>

<!-- System Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">System Status</h6>
                        <h4 class="mb-0">
                            <span class="status-indicator status-running"></span>
                            Running
                        </h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Models Trained</h6>
                        <h4 class="mb-0" id="models-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-brain fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Videos Generated</h6>
                        <h4 class="mb-0" id="videos-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-video fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Training Data</h6>
                        <h4 class="mb-0" id="training-data-count">0 GB</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="/upload" class="btn btn-primary btn-lg">
                                <i class="fas fa-upload me-2"></i>
                                Upload Videos
                            </a>
                        </div>
                        <small class="text-muted">Add training videos to your dataset</small>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button class="btn btn-success btn-lg" onclick="processVideos()">
                                <i class="fas fa-cogs me-2"></i>
                                Process Data
                            </button>
                        </div>
                        <small class="text-muted">Preprocess videos for training</small>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button class="btn btn-warning btn-lg" onclick="startTraining()">
                                <i class="fas fa-brain me-2"></i>
                                Start Training
                            </button>
                        </div>
                        <small class="text-muted">Train your AI model</small>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="/generation" class="btn btn-info btn-lg">
                                <i class="fas fa-magic me-2"></i>
                                Generate Videos
                            </a>
                        </div>
                        <small class="text-muted">Create new AI-generated videos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    System Performance
                </h5>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Hardware Configuration:</strong>
                    <div class="mt-2">
                        <div class="d-flex justify-content-between">
                            <span>Device:</span>
                            <span class="text-muted">{{ config.system_config.device or 'CPU' }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Resolution:</span>
                            <span class="text-muted">{{ config.data_config.target_resolution[0] }}x{{ config.data_config.target_resolution[1] }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Batch Size:</span>
                            <span class="text-muted">{{ config.training_config.batch_size }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Mixed Precision:</span>
                            <span class="text-muted">
                                {% if config.training_config.use_mixed_precision %}
                                    <i class="fas fa-check text-success"></i> Enabled
                                {% else %}
                                    <i class="fas fa-times text-danger"></i> Disabled
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>Model Architecture:</strong>
                    <div class="mt-2">
                        <div class="d-flex justify-content-between">
                            <span>Type:</span>
                            <span class="text-muted">Progressive ConvLSTM</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Attention:</span>
                            <span class="text-muted">
                                {% if config.model_config.use_attention %}
                                    <i class="fas fa-check text-success"></i> Enabled
                                {% else %}
                                    <i class="fas fa-times text-danger"></i> Disabled
                                {% endif %}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Progressive:</span>
                            <span class="text-muted">
                                {% if config.model_config.use_progressive %}
                                    <i class="fas fa-check text-success"></i> Enabled
                                {% else %}
                                    <i class="fas fa-times text-danger"></i> Disabled
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    Training Status
                </h5>
            </div>
            <div class="card-body">
                <div id="training-status-content">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>No training in progress. Upload videos and start training to begin.</p>
                    </div>
                </div>
                
                <div class="progress mt-3" style="display: none;" id="training-progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" id="training-progress-bar">
                        0%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Performance Chart
const ctx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'CPU Usage (%)',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }, {
            label: 'Memory Usage (%)',
            data: [],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        },
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// Update performance chart
function updatePerformanceChart() {
    fetch('/api/system_stats')
        .then(response => response.json())
        .then(data => {
            const now = new Date().toLocaleTimeString();
            
            // Add new data point
            performanceChart.data.labels.push(now);
            performanceChart.data.datasets[0].data.push(data.cpu_percent);
            performanceChart.data.datasets[1].data.push(data.memory_percent);
            
            // Keep only last 20 data points
            if (performanceChart.data.labels.length > 20) {
                performanceChart.data.labels.shift();
                performanceChart.data.datasets[0].data.shift();
                performanceChart.data.datasets[1].data.shift();
            }
            
            performanceChart.update('none');
        })
        .catch(error => console.log('Chart update failed:', error));
}

// Quick action functions
function processVideos() {
    if (confirm('Process all videos in the raw_videos folder?')) {
        fetch('/process_videos', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message + '. Found ' + data.sequences_count + ' sequences.');
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function startTraining() {
    const epochs = prompt('Number of epochs (default: 50):', '50');
    const batchSize = prompt('Batch size (default: 4):', '4');
    
    if (epochs && batchSize) {
        const formData = new FormData();
        formData.append('epochs', epochs);
        formData.append('batch_size', batchSize);
        
        fetch('/api/start_professional_training', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                num_epochs: parseInt(epochs),
                batch_size: parseInt(batchSize)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                startTrainingStatusPolling();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function startTrainingStatusPolling() {
    const statusDiv = document.getElementById('training-status-content');
    const progressBar = document.getElementById('training-progress');
    const progressBarInner = document.getElementById('training-progress-bar');
    
    progressBar.style.display = 'block';
    
    const interval = setInterval(() => {
        fetch('/api/training_status')
        .then(response => response.json())
        .then(data => {
            if (data.is_training) {
                statusDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Current Epoch:</strong> ${data.current_epoch} / ${data.total_epochs}
                        </div>
                        <div class="col-md-6">
                            <strong>Resolution:</strong> ${data.current_resolution}x${data.current_resolution}
                        </div>
                    </div>
                `;
                
                const progress = (data.current_epoch / data.total_epochs) * 100;
                progressBarInner.style.width = progress + '%';
                progressBarInner.textContent = Math.round(progress) + '%';
            } else {
                clearInterval(interval);
                progressBar.style.display = 'none';
                statusDiv.innerHTML = '<div class="text-center text-success py-4"><i class="fas fa-check-circle fa-2x mb-3"></i><p>Training completed!</p></div>';
            }
        });
    }, 2000);
}

// Update chart every 5 seconds
setInterval(updatePerformanceChart, 5000);
updatePerformanceChart(); // Initial load
</script>
{% endblock %}
