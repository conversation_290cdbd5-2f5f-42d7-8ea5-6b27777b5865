{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Generate Videos</h2>
        <p>Use your trained AI model to generate new videos.</p>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form id="generate-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Generation Mode</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="mode" id="mode-random" value="random" checked>
                            <label class="form-check-label" for="mode-random">
                                Random Generation
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="mode" id="mode-seed" value="seed">
                            <label class="form-check-label" for="mode-seed">
                                Seed-based Generation
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="seed-upload" style="display: none;">
                        <label for="seed-video" class="form-label">Seed Video</label>
                        <input type="file" class="form-control" id="seed-video" name="seed_video" accept=".mp4,.avi,.mov,.mkv">
                        <div class="form-text">Upload a video to use as seed for generation.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="num-frames" class="form-label">Number of Frames</label>
                        <input type="number" class="form-control" id="num-frames" name="frames" value="16" min="8" max="64">
                        <div class="form-text">Number of frames to generate (8-64).</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Generate Video</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Generation Modes</h5>
            </div>
            <div class="card-body">
                <h6>Random Generation</h6>
                <p class="small">Generate completely new videos from random noise.</p>
                
                <h6>Seed-based Generation</h6>
                <p class="small">Use an existing video as a starting point and generate continuation.</p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>Available Models</h5>
            </div>
            <div class="card-body">
                <div id="models-list">
                    <p>Loading models...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Generation Status</h5>
            </div>
            <div class="card-body">
                <div id="generation-status">
                    <p>No generation in progress</p>
                </div>
                <div class="progress" style="display: none;" id="generation-progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%">
                        Generating...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Generated Videos</h5>
            </div>
            <div class="card-body">
                <div id="generated-videos">
                    <p>No videos generated yet.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle seed upload based on mode selection
document.querySelectorAll('input[name="mode"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const seedUpload = document.getElementById('seed-upload');
        if (this.value === 'seed') {
            seedUpload.style.display = 'block';
        } else {
            seedUpload.style.display = 'none';
        }
    });
});

// Load available models
function loadModels() {
    fetch('/models')
    .then(response => response.json())
    .then(models => {
        const modelsList = document.getElementById('models-list');
        if (models.length === 0) {
            modelsList.innerHTML = '<p class="text-warning">No trained models found. Please train a model first.</p>';
        } else {
            let html = '<ul class="list-group list-group-flush">';
            models.forEach(model => {
                const sizeKB = Math.round(model.size / 1024);
                html += `<li class="list-group-item">
                    <strong>${model.filename}</strong><br>
                    <small class="text-muted">Size: ${sizeKB} KB | Modified: ${model.modified}</small>
                </li>`;
            });
            html += '</ul>';
            modelsList.innerHTML = html;
        }
    })
    .catch(error => {
        document.getElementById('models-list').innerHTML = '<p class="text-danger">Error loading models</p>';
    });
}

// Generate video form submission
document.getElementById('generate-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const statusDiv = document.getElementById('generation-status');
    const progressBar = document.getElementById('generation-progress');
    const videosDiv = document.getElementById('generated-videos');
    
    statusDiv.innerHTML = '<p>Starting video generation...</p>';
    progressBar.style.display = 'block';
    
    fetch('/generate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.display = 'none';
        
        if (data.success) {
            statusDiv.innerHTML = '<p class="text-success">' + data.message + '</p>';
            
            const videoHtml = `
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>Generated Video</h6>
                        <video controls width="100%" style="max-width: 500px;">
                            <source src="/${data.video_path}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <br>
                        <a href="/download/${data.video_path}" class="btn btn-sm btn-outline-primary mt-2">Download</a>
                    </div>
                </div>
            `;
            
            videosDiv.innerHTML = videoHtml + videosDiv.innerHTML;
        } else {
            statusDiv.innerHTML = '<p class="text-danger">Error: ' + data.error + '</p>';
        }
    })
    .catch(error => {
        progressBar.style.display = 'none';
        statusDiv.innerHTML = '<p class="text-danger">Generation failed: ' + error.message + '</p>';
    });
});

// Load models on page load
loadModels();
</script>
{% endblock %}
