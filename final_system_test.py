"""
Final comprehensive system test - Complete workflow verification
"""

import requests
import os
import json
import time

def test_complete_workflow():
    """Test the complete workflow end-to-end"""
    
    print("🚀 FINAL SYSTEM TEST - COMPLETE WORKFLOW")
    print("=" * 60)
    
    results = {
        "web_server": False,
        "system_monitoring": False,
        "dataset_management": False,
        "video_processing": False,
        "model_architecture": False,
        "video_generation": False,
        "file_outputs": False
    }
    
    # Test 1: Web Server Status
    print("1️⃣ Testing Web Server...")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("   ✅ Web server running")
            results["web_server"] = True
        else:
            print(f"   ❌ Web server error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Web server not accessible: {e}")
    
    # Test 2: System Monitoring API
    print("\n2️⃣ Testing System Monitoring...")
    try:
        response = requests.get("http://localhost:5000/api/system_stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ System monitoring active")
            print(f"   📊 CPU: {stats['cpu_percent']:.1f}%")
            print(f"   📊 Memory: {stats['memory_percent']:.1f}%")
            print(f"   📊 Disk: {stats['disk_usage']:.1f}%")
            results["system_monitoring"] = True
        else:
            print(f"   ❌ System monitoring failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ System monitoring error: {e}")
    
    # Test 3: Dataset Management
    print("\n3️⃣ Testing Dataset Management...")
    try:
        response = requests.get("http://localhost:5000/api/dataset_info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Dataset management working")
            print(f"   📹 Videos: {data['total_videos']}")
            print(f"   💾 Size: {data['total_size_mb']} MB")
            print(f"   🔄 Processed: {data['processed_videos']} sequences")
            results["dataset_management"] = True
        else:
            print(f"   ❌ Dataset management failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dataset management error: {e}")
    
    # Test 4: Video Processing
    print("\n4️⃣ Testing Video Processing...")
    try:
        response = requests.post("http://localhost:5000/process_videos", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ Video processing working")
                print(f"   🎬 Sequences: {data['sequences_count']}")
                results["video_processing"] = True
            else:
                print(f"   ❌ Video processing failed: {data.get('error')}")
        else:
            print(f"   ❌ Video processing HTTP error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Video processing error: {e}")
    
    # Test 5: Model Architecture
    print("\n5️⃣ Testing Model Architecture...")
    try:
        import torch
        from model import ProgressiveVideoGenerator, VideoDiscriminator
        
        # Test generator
        generator = ProgressiveVideoGenerator(
            input_channels=3,
            hidden_dims=[32, 64],
            num_layers=2,
            use_attention=False,
            use_progressive=False
        )
        
        # Test discriminator
        discriminator = VideoDiscriminator(
            input_channels=3,
            hidden_dims=[32, 64]
        )
        
        # Test forward pass
        test_input = torch.randn(1, 8, 3, 128, 128)
        with torch.no_grad():
            generated = generator(test_input, future_frames=4)
            disc_output = discriminator(generated)
        
        print(f"   ✅ Model architecture working")
        print(f"   🧠 Generator output: {generated.shape}")
        print(f"   🧠 Discriminator output: {disc_output.shape}")
        results["model_architecture"] = True
        
    except Exception as e:
        print(f"   ❌ Model architecture error: {e}")
    
    # Test 6: Video Generation
    print("\n6️⃣ Testing Video Generation...")
    try:
        # Check if generated videos exist
        output_dir = "outputs/generated_videos"
        expected_files = ["sample_generated_video.mp4", "input_sequence.mp4"]
        
        generated_files = []
        for file in expected_files:
            filepath = os.path.join(output_dir, file)
            if os.path.exists(filepath):
                size_mb = os.path.getsize(filepath) / (1024*1024)
                generated_files.append(f"{file} ({size_mb:.1f}MB)")
        
        if generated_files:
            print(f"   ✅ Video generation working")
            for file in generated_files:
                print(f"   🎥 Generated: {file}")
            results["video_generation"] = True
        else:
            print(f"   ❌ No generated videos found")
            
    except Exception as e:
        print(f"   ❌ Video generation test error: {e}")
    
    # Test 7: File Outputs
    print("\n7️⃣ Testing File Outputs...")
    try:
        # Check directory structure
        important_dirs = [
            "data/raw_videos",
            "data/processed_videos", 
            "outputs/generated_videos",
            "models/checkpoints",
            "logs"
        ]
        
        existing_dirs = []
        for dir_path in important_dirs:
            if os.path.exists(dir_path):
                file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
                existing_dirs.append(f"{dir_path} ({file_count} files)")
        
        if len(existing_dirs) >= 3:  # At least 3 important directories exist
            print(f"   ✅ File structure working")
            for dir_info in existing_dirs:
                print(f"   📁 {dir_info}")
            results["file_outputs"] = True
        else:
            print(f"   ❌ Incomplete file structure")
            
    except Exception as e:
        print(f"   ❌ File structure test error: {e}")
    
    # Final Results
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    print(f"\n📈 OVERALL SCORE: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests >= 5:  # At least 5/7 tests pass
        print("\n🎉 SYSTEM STATUS: OPERATIONAL")
        print("🚀 The Professional Video AI Generator is working!")
        
        print(f"\n🎯 WHAT'S WORKING:")
        working_features = [name.replace('_', ' ').title() for name, status in results.items() if status]
        for feature in working_features:
            print(f"   ✅ {feature}")
        
        print(f"\n🌐 ACCESS YOUR SYSTEM:")
        print(f"   🔗 Web Interface: http://localhost:5000")
        print(f"   📁 Generated Videos: outputs/generated_videos/")
        print(f"   📊 Training Data: data/raw_videos/")
        
        print(f"\n🎬 READY FOR:")
        print(f"   📤 Upload your own videos")
        print(f"   🏋️ Train custom AI models") 
        print(f"   🎨 Generate new videos")
        
    else:
        print("\n⚠️ SYSTEM STATUS: PARTIAL")
        print("Some components need attention, but core functionality works.")
    
    print("=" * 60)
    
    return passed_tests, total_tests

if __name__ == "__main__":
    passed, total = test_complete_workflow()
    
    if passed >= 5:
        print(f"\n🎊 SUCCESS! Your Professional Video AI Generator is ready to use!")
    else:
        print(f"\n🔧 Some components need fixing, but the system is partially functional.")
