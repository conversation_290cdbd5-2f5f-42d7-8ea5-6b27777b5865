"""
Simple training test to verify the training pipeline works
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import os
import json
from tqdm import tqdm

# Import our components
from model import ProgressiveVideoGenerator, VideoDiscriminator
from train_model import VideoDataset

def test_simple_training():
    """Test a simple training loop"""
    print("🧠 Testing Simple Training Pipeline...")
    
    # Check if we have processed data
    if not os.path.exists("data/processed_videos/metadata.json"):
        print("❌ No processed video data found!")
        return False
    
    # Load metadata
    with open("data/processed_videos/metadata.json", 'r') as f:
        metadata = json.load(f)
    
    print(f"📊 Found {len(metadata)} processed sequences")
    
    # Create dataset and dataloader
    try:
        dataset = VideoDataset(sequence_length=16)
        if len(dataset) == 0:
            print("❌ Dataset is empty!")
            return False
        
        print(f"✅ Dataset loaded: {len(dataset)} sequences")
        
        # Create small dataloader for testing
        dataloader = DataLoader(dataset, batch_size=1, shuffle=True, num_workers=0)
        
        # Test loading a batch
        for batch in dataloader:
            print(f"✅ Batch loaded: {batch.shape}")
            test_batch = batch
            break
        
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return False
    
    # Create models with smaller dimensions for testing
    try:
        device = torch.device('cpu')  # Force CPU for testing
        
        generator = ProgressiveVideoGenerator(
            input_channels=3,
            hidden_dims=[32, 64],  # Smaller for testing
            num_layers=2,
            sequence_length=16,
            use_attention=False,  # Disable attention for simplicity
            use_progressive=False
        ).to(device)
        
        discriminator = VideoDiscriminator(
            input_channels=3,
            hidden_dims=[32, 64]
        ).to(device)
        
        print("✅ Models created successfully")
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return False
    
    # Create optimizers
    try:
        optimizer_g = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        optimizer_d = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        
        # Loss functions
        adversarial_loss = nn.BCEWithLogitsLoss()
        reconstruction_loss = nn.MSELoss()
        
        print("✅ Optimizers and losses created")
        
    except Exception as e:
        print(f"❌ Optimizer creation failed: {e}")
        return False
    
    # Test training step
    try:
        print("🔄 Testing training step...")
        
        # Get a batch
        real_videos = test_batch.to(device)
        batch_size = real_videos.size(0)
        
        # Split into input and target
        input_frames = 8
        if real_videos.size(1) < 16:
            print(f"❌ Video sequence too short: {real_videos.size(1)} frames")
            return False
        
        input_sequence = real_videos[:, :input_frames]
        target_sequence = real_videos[:, input_frames:]
        
        print(f"   Input shape: {input_sequence.shape}")
        print(f"   Target shape: {target_sequence.shape}")
        
        # Generate fake videos
        with torch.no_grad():  # Test without gradients first
            fake_videos = generator(input_sequence, future_frames=target_sequence.size(1))
            print(f"   Generated shape: {fake_videos.shape}")
        
        # Test discriminator
        with torch.no_grad():
            real_output = discriminator(target_sequence)
            fake_output = discriminator(fake_videos)
            print(f"   Discriminator real output: {real_output.shape}")
            print(f"   Discriminator fake output: {fake_output.shape}")
        
        print("✅ Forward pass successful")
        
        # Test one training step with gradients
        print("🔄 Testing training step with gradients...")
        
        # Train Discriminator
        optimizer_d.zero_grad()
        
        real_labels = torch.ones(batch_size, 1)
        fake_labels = torch.zeros(batch_size, 1)
        
        real_output = discriminator(target_sequence)
        fake_videos = generator(input_sequence, future_frames=target_sequence.size(1))
        fake_output = discriminator(fake_videos.detach())
        
        d_loss_real = adversarial_loss(real_output, real_labels)
        d_loss_fake = adversarial_loss(fake_output, fake_labels)
        d_loss = (d_loss_real + d_loss_fake) / 2
        
        d_loss.backward()
        optimizer_d.step()
        
        print(f"   Discriminator loss: {d_loss.item():.4f}")
        
        # Train Generator
        optimizer_g.zero_grad()
        
        fake_videos = generator(input_sequence, future_frames=target_sequence.size(1))
        fake_output = discriminator(fake_videos)
        
        g_loss_adv = adversarial_loss(fake_output, real_labels)
        g_loss_recon = reconstruction_loss(fake_videos, target_sequence)
        g_loss = g_loss_adv + 10.0 * g_loss_recon
        
        g_loss.backward()
        optimizer_g.step()
        
        print(f"   Generator loss: {g_loss.item():.4f}")
        print(f"   Reconstruction loss: {g_loss_recon.item():.4f}")
        
        print("✅ Training step successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Training step failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_generation():
    """Test video generation"""
    print("\n🎨 Testing Video Generation...")
    
    try:
        device = torch.device('cpu')
        
        # Create a simple generator
        generator = ProgressiveVideoGenerator(
            input_channels=3,
            hidden_dims=[32, 64],
            num_layers=2,
            sequence_length=16,
            use_attention=False,
            use_progressive=False
        ).to(device)
        
        # Create random input
        batch_size = 1
        seq_len = 8
        channels = 3
        height, width = 64, 64
        
        input_sequence = torch.randn(batch_size, seq_len, channels, height, width)
        
        # Generate video
        with torch.no_grad():
            generated_video = generator(input_sequence, future_frames=8)
            print(f"✅ Generated video shape: {generated_video.shape}")
        
        # Convert to numpy for saving (simplified)
        generated_np = generated_video.squeeze(0).cpu().numpy()
        print(f"✅ Converted to numpy: {generated_np.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        return False

def main():
    """Run simple training test"""
    print("🚀 SIMPLE TRAINING PIPELINE TEST")
    print("=" * 50)
    
    # Test 1: Simple Training
    training_ok = test_simple_training()
    
    # Test 2: Video Generation
    generation_ok = test_video_generation()
    
    print("\n" + "=" * 50)
    print("📋 SIMPLE TEST RESULTS:")
    print(f"✅ Training Pipeline: {'Working' if training_ok else 'Failed'}")
    print(f"✅ Video Generation: {'Working' if generation_ok else 'Failed'}")
    
    if training_ok and generation_ok:
        print("\n🎉 ALL CORE COMPONENTS WORKING!")
        print("The system is ready for full training and generation.")
    else:
        print("\n❌ Some components need fixing.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
