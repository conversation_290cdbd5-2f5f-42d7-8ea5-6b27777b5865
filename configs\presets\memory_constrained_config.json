{"model": {"input_channels": 3, "hidden_dims": [32, 64], "disc_hidden_dims": [64, 128, 256, 512], "kernel_size": [3, 3], "num_layers": 4, "sequence_length": 16, "use_attention": true, "use_progressive": true, "max_resolution": 64, "dropout_rate": 0.1}, "training": {"batch_size": 1, "num_epochs": 200, "lr_g": 0.0002, "lr_d": 0.0002, "beta1": 0.5, "beta2": 0.999, "weight_decay": 0.0001, "progressive_epochs": [20, 40, 60], "progressive_batch_sizes": [8, 6, 4], "adversarial_weight": 1.0, "reconstruction_weight": 10.0, "perceptual_weight": 5.0, "temporal_weight": 2.0, "gradient_weight": 1.0, "use_mixed_precision": true, "gradient_clip_val": 1.0, "accumulate_grad_batches": 1, "scheduler_type": "cosine_warm_restarts", "warmup_epochs": 5, "early_stopping_patience": 15, "early_stopping_min_delta": 0.0001}, "data": {"data_dir": "data/processed_videos", "raw_data_dir": "data/raw_videos", "sequence_length": 16, "target_fps": 30, "target_resolution": [64, 64], "use_augmentation": true, "horizontal_flip_prob": 0.5, "rotation_range": 10, "brightness_range": 0.2, "contrast_range": 0.2, "num_workers": 4, "pin_memory": true, "prefetch_factor": 2}, "system": {"device": "auto", "multi_gpu": false, "mixed_precision": false, "compile_model": false, "gradient_checkpointing": true, "cpu_offload": true, "use_wandb": false, "wandb_project": "professional-video-ai", "log_interval": 10, "save_interval": 5, "checkpoint_dir": "models/checkpoints", "output_dir": "outputs/generated_videos", "log_dir": "logs"}}