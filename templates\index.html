{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="jumbotron bg-primary text-white p-5 rounded">
            <h1 class="display-4">Video AI Generator</h1>
            <p class="lead">Train your own AI model to generate videos from your training data.</p>
            <hr class="my-4">
            <p>Upload videos, train the model, and generate new content!</p>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">1. Upload Videos</h5>
                <p class="card-text">Upload your training videos to teach the AI model.</p>
                <a href="/upload" class="btn btn-primary">Upload Videos</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">2. Train Model</h5>
                <p class="card-text">Process videos and train the AI model.</p>
                <button class="btn btn-warning" onclick="processVideos()">Process Videos</button>
                <button class="btn btn-success mt-2" onclick="startTraining()">Start Training</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">3. Generate Videos</h5>
                <p class="card-text">Use the trained model to generate new videos.</p>
                <a href="/generate" class="btn btn-info">Generate Videos</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Training Status</h5>
            </div>
            <div class="card-body">
                <div id="training-status">
                    <p>No training in progress</p>
                </div>
                <div class="progress" style="display: none;" id="progress-bar">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function processVideos() {
    fetch('/process_videos', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message + '. Found ' + data.sequences_count + ' sequences.');
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function startTraining() {
    const epochs = prompt('Number of epochs (default: 50):', '50');
    const batchSize = prompt('Batch size (default: 4):', '4');
    
    const formData = new FormData();
    formData.append('epochs', epochs || '50');
    formData.append('batch_size', batchSize || '4');
    
    fetch('/train', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            startStatusPolling();
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function startStatusPolling() {
    const statusDiv = document.getElementById('training-status');
    const progressBar = document.getElementById('progress-bar');
    const progressBarInner = progressBar.querySelector('.progress-bar');
    
    progressBar.style.display = 'block';
    
    const interval = setInterval(() => {
        fetch('/training_status')
        .then(response => response.json())
        .then(data => {
            statusDiv.innerHTML = '<p>' + data.message + '</p>';
            progressBarInner.style.width = data.progress + '%';
            progressBarInner.textContent = data.progress + '%';
            
            if (!data.is_training) {
                clearInterval(interval);
                if (data.progress === 100) {
                    progressBar.style.display = 'none';
                    statusDiv.innerHTML = '<p class="text-success">' + data.message + '</p>';
                }
            }
        });
    }, 2000);
}

// Check training status on page load
fetch('/training_status')
.then(response => response.json())
.then(data => {
    if (data.is_training) {
        startStatusPolling();
    }
});
</script>
{% endblock %}
