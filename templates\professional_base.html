<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Video AI Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .main-content { background-color: #f8f9fa; min-height: 100vh; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); border: none; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; }
        .status-running { background-color: #28a745; animation: pulse 2s infinite; }
        .status-idle { background-color: #6c757d; }
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 sidebar p-3">
                <h4 class="text-white mb-4"><i class="fas fa-video"></i> Video AI Pro</h4>
                <ul class="nav flex-column">
                    <li class="nav-item"><a class="nav-link text-white" href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/configuration"><i class="fas fa-cog"></i> Configuration</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/training"><i class="fas fa-brain"></i> Training</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/generation"><i class="fas fa-magic"></i> Generation</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="/analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </nav>
            <main class="col-md-10 main-content p-4">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>